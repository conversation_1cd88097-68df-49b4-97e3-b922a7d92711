import * as React from "react"

import { cn } from "@/lib/utils"

const Chart = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(({ className, ...props }, ref) => {
  return <div className={cn("relative", className)} ref={ref} {...props} />
})
Chart.displayName = "Chart"

const ChartContainer = React.forwardRef<HTMLDivElement, React.PropsWithChildren<{ data: any[] }>>(
  ({ className, data, children, ...props }, ref) => {
    return (
      <div className={cn("grid w-full h-full aspect-square", className)} ref={ref} {...props}>
        {children}
      </div>
    )
  },
)
ChartContainer.displayName = "ChartContainer"

interface ChartTooltipProps {
  children: React.ReactNode
}

const ChartTooltip = ({ children }: ChartTooltipProps) => {
  return <>{children}</>
}
ChartTooltip.displayName = "ChartTooltip"

const ChartTooltipContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return <div className={cn("pointer-events-none absolute z-50", className)} ref={ref} {...props} />
  },
)
ChartTooltipContent.displayName = "ChartTooltipContent"

const ChartArea = React.forwardRef<SVGAElement, React.SVGAttributes<SVGAElement>>(({ className, ...props }, ref) => {
  return <area className={cn("fill-primary stroke-none", className)} ref={ref} {...props} />
})
ChartArea.displayName = "ChartArea"

const ChartLine = React.forwardRef<SVGAElement, React.SVGAttributes<SVGAElement>>(({ className, ...props }, ref) => {
  return <line className={cn("stroke-primary", className)} ref={ref} {...props} />
})
ChartLine.displayName = "ChartLine"

const ChartHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return <div className={cn("flex flex-col space-y-1.5", className)} ref={ref} {...props} />
  },
)
ChartHeader.displayName = "ChartHeader"

const ChartTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => {
    return <p className={cn("text-lg font-semibold", className)} ref={ref} {...props} />
  },
)
ChartTitle.displayName = "ChartTitle"

const ChartDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => {
    return <p className={cn("text-sm text-muted-foreground", className)} ref={ref} {...props} />
  },
)
ChartDescription.displayName = "ChartDescription"

const ChartLegend = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return <div className={cn("flex justify-center pt-4", className)} ref={ref} {...props} />
  },
)
ChartLegend.displayName = "ChartLegend"

const ChartLegendItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return <div className={cn("flex items-center space-x-2", className)} ref={ref} {...props} />
  },
)
ChartLegendItem.displayName = "ChartLegendItem"

export {
  Chart,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartArea,
  ChartLine,
  ChartHeader,
  ChartTitle,
  ChartDescription,
  ChartLegend,
  ChartLegendItem,
}
