import { StatusBar } from "expo-status-bar"
import { SafeAreaProvider } from "react-native-safe-area-context"
import { NavigationContainer } from "@react-navigation/native"
import { ThemeProvider } from "./src/context/ThemeContext"
import { LocationProvider } from "./src/context/LocationContext"
import { AuthProvider } from "./src/context/AuthContext"
import { BookingProvider } from "./src/context/BookingContext"
import { NotificationProvider } from "./src/context/NotificationContext"
import { ProviderAvailabilityProvider } from "./src/context/ProviderAvailabilityContext"
import RootNavigator from "./src/navigation/RootNavigator"
import { GestureHandlerRootView } from "react-native-gesture-handler"
import { ToastProvider, ToastInitializer } from "./src/components/ToastManager"

// Import dev utils for development testing
if (__DEV__) {
  require("./src/utils/devUtils");
}

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <ToastProvider>
            <AuthProvider>
              <BookingProvider>
                <NotificationProvider>
                  <ProviderAvailabilityProvider>
                    <LocationProvider>
                      <NavigationContainer
                        theme={{
                          dark: false,
                          colors: {
                            primary: "#6FD1FF",
                            background: "#F5F5F5",
                            card: "#FFFFFF",
                            text: "#333333",
                            border: "#E1E1E1",
                            notification: "#FF4D4F",
                          },
                        }}
                      >
                        <StatusBar style="auto" />
                        <ToastInitializer />
                        <RootNavigator />
                      </NavigationContainer>
                    </LocationProvider>
                  </ProviderAvailabilityProvider>
                </NotificationProvider>
              </BookingProvider>
            </AuthProvider>
          </ToastProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  )
}
