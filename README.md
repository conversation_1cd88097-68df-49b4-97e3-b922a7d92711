# CleanConnect App

CleanConnect is a mobile application for booking cleaning services, built with React Native and Expo.

## Project Structure

The project is now structured with a clear separation between the frontend and backend:

- `/src` - Contains the React Native frontend application
- `/backend-exp` - (To be created) Will contain the Express.js backend

## Frontend

The frontend is a React Native application built with Expo. It communicates with the backend API through a centralized configuration.

### Environment Configuration

Create a `.env` file in the root directory with the following variables:

```
API_URL=http://***********:3000
```

Replace the IP address with your actual backend server IP address.

### API Configuration

The frontend uses a centralized API configuration in `src/api/config.ts`. All API requests are made through this configuration, which automatically appends `/api` to the base URL.

## Backend

The backend has been decoupled from the frontend and should be implemented as a separate Express.js application in a `backend-exp` folder. The Next.js API routes have been removed.

### Backend Implementation Guidelines

1. Create a new Express.js application in a `backend-exp` folder
2. Implement the same API endpoints that were previously in the Next.js API routes
3. Use PostgreSQL with Prisma as the database ORM
4. Implement JWT authentication
5. Use Nodemailer for OTP verification
6. Ensure all endpoints are properly secured with authentication middleware where needed

### API Endpoints

The backend should implement the following API endpoints:

- Authentication
  - POST /api/auth/register
  - POST /api/auth/login
  - GET /api/auth/verify
  - POST /api/auth/otp/send
  - POST /api/auth/otp/verify

- Profile
  - GET /api/profile
  - PUT /api/profile/update

- Addresses
  - GET /api/addresses
  - POST /api/addresses
  - GET /api/addresses/:id
  - PUT /api/addresses/:id
  - DELETE /api/addresses/:id

- Services
  - GET /api/services
  - GET /api/services/:id

- Bookings
  - GET /api/bookings
  - POST /api/bookings
  - GET /api/bookings/:id
  - PUT /api/bookings/:id

- Payments
  - POST /api/payments
  - POST /api/payments/receipt

## Running the Application

1. Start the Express.js backend:
   ```
   cd backend-exp
   npm start
   ```

2. Start the React Native frontend:
   ```
   npm start
   ```

3. Use Expo Go on your mobile device to connect to the development server.
