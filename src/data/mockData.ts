// Mock data for the app
import { UserRole } from '../types/user';
import { BookingStatus } from '../types/booking';
import { v4 as uuidv4 } from 'uuid';

// Mock users
export const users = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: 'Doe',
    role: 'CUSTOMER' as UserRole,
    phone: '+**********',
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: 'Smith',
    role: 'PROVIDER' as UserRole,
    phone: '+**********',
  },
];

// Mock services
export const services = [
  {
    id: '1',
    title: 'Regular Home Cleaning',
    description: 'Standard cleaning service for homes',
    image: 'https://picsum.photos/id/26/600/300',
    price: 100,
    duration: '2 hours',
    category: 'HOME',
  },
  {
    id: '2',
    title: 'Deep Cleaning',
    description: 'Thorough cleaning of all areas',
    image: 'https://picsum.photos/id/28/600/300',
    price: 200,
    duration: '4 hours',
    category: 'HOME',
  },
  {
    id: '3',
    title: 'Office Cleaning',
    description: 'Professional cleaning for offices',
    image: 'https://picsum.photos/id/30/600/300',
    price: 150,
    duration: '3 hours',
    category: 'OFFICE',
  },
];

// Mock addresses
export const addresses = [
  {
    id: '1',
    label: 'Home',
    address: '123 Main St, City',
    area: 'Downtown',
    isPrimary: true,
    latitude: 37.7749,
    longitude: -122.4194,
  },
  {
    id: '2',
    label: 'Office',
    address: '456 Business Ave, City',
    area: 'Business District',
    isPrimary: false,
    latitude: 37.7833,
    longitude: -122.4167,
  },
];

// Mock providers
export const providers = [
  {
    id: '1',
    name: 'Mariama Jallow',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    rating: 4.9,
    jobs: 120,
    verified: true,
  },
  {
    id: '2',
    name: 'Ousman Ceesay',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    rating: 4.7,
    jobs: 85,
    verified: true,
  },
  {
    id: '3',
    name: 'Fatou Saine',
    image: 'https://randomuser.me/api/portraits/women/68.jpg',
    rating: 4.8,
    jobs: 95,
    verified: true,
  },
];

// Mock bookings
export const bookings = [
  {
    id: 'BK-12345',
    serviceId: '1',
    serviceName: 'Regular Home Cleaning',
    serviceImage: 'https://picsum.photos/id/26/600/300',
    date: '2023-06-15',
    time: '10:00 AM',
    duration: '2 hours',
    status: 'COMPLETED' as BookingStatus,
    price: 250,
    address: {
      id: 'addr1',
      label: 'Home',
      address: '123 Kairaba Avenue, Serrekunda',
    },
    provider: {
      id: 'prov1',
      name: 'Mariama Jallow',
      image: 'https://randomuser.me/api/portraits/women/44.jpg',
      phone: '+220 7123456',
      rating: 4.9,
    },
    paymentMethod: 'Mobile Money',
    paymentStatus: 'PAID',
    notes: 'Please focus on kitchen and bathrooms',
  },
  {
    id: 'BK-12346',
    serviceId: '2',
    serviceName: 'Deep Cleaning',
    serviceImage: 'https://picsum.photos/id/28/600/300',
    date: '2023-07-20',
    time: '09:00 AM',
    duration: '4 hours',
    status: 'CONFIRMED' as BookingStatus,
    price: 450,
    address: {
      id: 'addr2',
      label: 'Office',
      address: '45 Kairaba Avenue, Serrekunda',
    },
    provider: {
      id: 'prov2',
      name: 'Ousman Ceesay',
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      phone: '+220 7654321',
      rating: 4.7,
    },
    paymentMethod: 'Pay Later',
    paymentStatus: 'PENDING',
    notes: 'Need thorough cleaning of all areas',
  },
];

// Helper functions for mock API
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const generateSuccessResponse = (data: any) => ({
  success: true,
  data,
  message: 'Operation successful',
});

export const generateErrorResponse = (error: string) => ({
  success: false,
  data: null,
  error,
});

export const generateId = () => uuidv4();
