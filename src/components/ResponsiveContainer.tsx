import type React from "react"
import { View, StyleSheet, type ViewStyle, type StyleProp } from "react-native"

interface ResponsiveContainerProps {
  children: React.ReactNode
  style?: StyleProp<ViewStyle>
}

const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({ children, style }) => {
  const styles = StyleSheet.create({
    container: {
      width: "100%",
      paddingHorizontal: 16,
    },
  })

  return <View style={[styles.container, style]}>{children}</View>
}

export default ResponsiveContainer
