"use client"

import type React from "react"
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import Card from "./Card"
import Button from "./Button"

interface ServiceCardProps {
  id: string
  title: string
  description: string
  price: number
  duration: string
  rating: number
  reviews: number
  image: string
  featured?: boolean
  onPress: () => void
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  title,
  description,
  price,
  duration,
  rating,
  reviews,
  image,
  featured = false,
  onPress,
}) => {
  const theme = useTheme()

  const styles = StyleSheet.create({
    container: {
      marginBottom: theme.spacing.md,
    },
    imageContainer: {
      position: "relative",
    },
    image: {
      width: "100%",
      height: 160,
      borderTopLeftRadius: theme.borderRadius.md,
      borderTopRightRadius: theme.borderRadius.md,
    },
    featuredBadge: {
      position: "absolute",
      top: theme.spacing.sm,
      left: theme.spacing.sm,
      backgroundColor: theme.colors.warning,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      borderRadius: theme.borderRadius.sm,
    },
    featuredText: {
      color: "black",
      fontWeight: "600",
      fontSize: theme.fontSizes.xs,
    },
    content: {
      padding: theme.spacing.md,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: theme.spacing.sm,
    },
    title: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      color: theme.colors.text,
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    price: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "700",
      color: theme.colors.text,
    },
    description: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
      marginBottom: theme.spacing.md,
    },
    footer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    star: {
      color: "#FFD700",
      marginRight: 4,
    },
    ratingText: {
      fontSize: theme.fontSizes.sm,
      fontWeight: "500",
      marginRight: 4,
    },
    reviewsText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    durationText: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
  })

  return (
    <Card style={styles.container}>
      <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
        <View style={styles.imageContainer}>
          <Image source={{ uri: image }} style={styles.image} resizeMode="cover" />
          {featured && (
            <View style={styles.featuredBadge}>
              <Text style={styles.featuredText}>Featured</Text>
            </View>
          )}
        </View>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.price}>D{price}/hr</Text>
          </View>
          <Text style={styles.description} numberOfLines={2}>
            {description}
          </Text>
          <View style={styles.footer}>
            <View style={styles.ratingContainer}>
              <Feather name="star" size={16} style={styles.star} />
              <Text style={styles.ratingText}>{rating}</Text>
              <Text style={styles.reviewsText}>({reviews})</Text>
            </View>
            <Text style={styles.durationText}>Est. {duration}</Text>
          </View>
          <Button title="Book Now" onPress={onPress} variant="warning" fullWidth />
        </View>
      </TouchableOpacity>
    </Card>
  )
}

export default ServiceCard
