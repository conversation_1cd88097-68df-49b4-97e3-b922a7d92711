import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Keyboard,
  Modal,
  Dimensions,
  Animated,
  Vibration,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import Button from './Button';
import otpService from '../api/services/otpService';
import { useAuth } from '../context/AuthContext';

interface OtpVerificationModalProps {
  visible: boolean;
  onClose: () => void;
  email?: string;
  phone?: string;
  purpose: 'REGISTER' | 'LOGIN' | 'RESET';
  onSuccess?: () => void;
  userRole?: string;
  returnTo?: string;
  returnParams?: any;
  devOtp?: string; // For development testing
}

const OtpVerificationModal: React.FC<OtpVerificationModalProps> = ({
  visible,
  onClose,
  email,
  phone,
  purpose,
  onSuccess,
  userRole,
  returnTo,
  returnParams,
  devOtp,
}) => {
  const theme = useTheme();
  const { login } = useAuth();

  // State for OTP input
  const [otp, setOtp] = useState(['', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [verificationError, setVerificationError] = useState('');
  const [verificationSuccess, setVerificationSuccess] = useState(false);

  // Animation values
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  const successOpacity = useRef(new Animated.Value(0)).current;

  // Refs for input fields
  const inputRefs = useRef<Array<TextInput | null>>([null, null, null, null]);

  // Reset OTP and state when modal is opened
  useEffect(() => {
    if (visible) {
      setOtp(['', '', '', '']);
      setTimeLeft(60);
      setCanResend(false);
      setVerificationError('');
      setVerificationSuccess(false);
      successOpacity.setValue(0);

      // If dev OTP is provided, auto-fill it
      if (devOtp && devOtp.length === 4) {
        const otpDigits = devOtp.split('');
        setOtp(otpDigits);
      }

      // Focus the first input field after a short delay
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 300);
    }
  }, [visible, devOtp]);

  // Timer for OTP resend
  useEffect(() => {
    if (!visible) return;

    if (timeLeft > 0) {
      const timerId = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timerId);
    } else {
      setCanResend(true);
    }
  }, [timeLeft, visible]);

  // Shake animation for error
  const startShakeAnimation = () => {
    Vibration.vibrate(400);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();
  };

  // Success animation
  const startSuccessAnimation = () => {
    Animated.timing(successOpacity, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true
    }).start();
  };

  // Handle OTP input change
  const handleOtpChange = (text: string, index: number) => {
    // Clear any previous errors
    if (verificationError) {
      setVerificationError('');
    }

    // Handle paste of full OTP code
    if (text.length > 1) {
      // Check if it's a full 4-digit code being pasted
      if (text.length === 4 && /^\d{4}$/.test(text)) {
        const newOtp = text.split('');
        setOtp(newOtp);

        // Move focus to the last input
        inputRefs.current[3]?.focus();

        // Auto-verify after a short delay
        setTimeout(() => {
          handleVerifyOtp(newOtp.join(''));
        }, 300);
        return;
      }

      // Otherwise just take the first character
      text = text[0];
    }

    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Auto-focus next input
    if (text && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-verify if all digits are entered
    if (text && index === 3) {
      const completeOtp = [...newOtp].join('');
      if (completeOtp.length === 4 && /^\d{4}$/.test(completeOtp)) {
        setTimeout(() => {
          handleVerifyOtp(completeOtp);
        }, 300);
      }
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async (otpCode?: string) => {
    const codeToVerify = otpCode || otp.join('');

    // Clear previous errors
    setVerificationError('');

    if (codeToVerify.length !== 4) {
      setVerificationError('Please enter a valid 4-digit verification code');
      startShakeAnimation();
      return;
    }

    try {
      setIsLoading(true);
      Keyboard.dismiss();

      // Prepare verification data based on what we have
      const verifyData: any = {
        code: codeToVerify,
        purpose,
      };

      if (email) {
        verifyData.email = email;
      } else if (phone) {
        verifyData.phone = phone;
      } else {
        setVerificationError('No email or phone provided for verification');
        startShakeAnimation();
        setIsLoading(false);
        return;
      }

      // Call the backend API to verify the OTP
      const response = await otpService.verifyOtp(verifyData);

      console.log('OTP verification response:', response);

      if (!response.success) {
        setVerificationError(response.error || 'Invalid verification code. Please try again.');
        startShakeAnimation();
        setIsLoading(false);
        return;
      }

      // Show success animation
      setVerificationSuccess(true);
      startSuccessAnimation();

      // Wait a moment to show the success animation before proceeding
      setTimeout(() => {
        // Handle different verification purposes
        if (purpose === 'REGISTER') {
          // For registration, proceed without showing an alert
          onClose();
          if (onSuccess) onSuccess();
        } else if (purpose === 'LOGIN') {
          // For login verification, proceed without showing an alert
          onClose();
          if (onSuccess) onSuccess();
        } else if (purpose === 'RESET') {
          // For password reset, proceed without showing an alert
          onClose();
          if (onSuccess) onSuccess();
        }
      }, 1000);
    } catch (error: any) {
      console.error('OTP verification error:', error);
      setVerificationError(error.message || 'An error occurred during verification. Please try again.');
      startShakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP resend
  const handleResendOtp = async () => {
    if (!canResend) return;

    try {
      setIsLoading(true);
      setVerificationError('');

      // Reset OTP input fields
      setOtp(['', '', '', '']);

      // Focus the first input field
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);

      // Prepare resend data based on what we have
      const resendData: any = {
        purpose: purpose,
      };

      if (email) {
        resendData.email = email;
      } else if (phone) {
        resendData.phone = phone;
      } else {
        setVerificationError('No email or phone provided for verification');
        startShakeAnimation();
        setIsLoading(false);
        return;
      }

      // Call OTP send API
      const response = await otpService.sendOtp(resendData);

      if (response.success) {
        // Reset timer
        setTimeLeft(60);
        setCanResend(false);

        // If in development and OTP is returned, auto-fill it
        if (response.data?.otp && response.data.otp.length === 4) {
          const otpDigits = response.data.otp.split('');
          setOtp(otpDigits);
        }

        // Show success message without blocking alert
        setVerificationError('');
        // Use a temporary success message instead of an alert
        const originalError = verificationError;
        setVerificationError(email
          ? 'A new verification code has been sent to your email'
          : 'A new verification code has been sent to your phone');
        setTimeout(() => {
          // Clear the success message after a few seconds
          setVerificationError(originalError);
        }, 3000);
      } else {
        setVerificationError(response.error || 'Failed to resend verification code. Please try again.');
        startShakeAnimation();
      }
    } catch (error: any) {
      console.error('OTP resend error:', error);
      setVerificationError(error.message || 'Failed to resend verification code. Please try again.');
      startShakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              accessibilityLabel="Close verification modal"
            >
              <Feather name="x" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            {/* Success animation overlay */}
            {verificationSuccess && (
              <Animated.View
                style={[
                  styles.successOverlay,
                  { opacity: successOpacity, backgroundColor: theme.colors.background }
                ]}
              >
                <View style={styles.successIconContainer}>
                  <Feather name="check-circle" size={60} color={theme.colors.success} />
                  <Text style={[styles.successText, { color: theme.colors.text }]}>
                    Verification Successful
                  </Text>
                </View>
              </Animated.View>
            )}

            <Text style={[styles.title, { color: theme.colors.text }]}>
              Verification Code
            </Text>

            <Text style={[styles.subtitle, { color: theme.colors.textLight }]}>
              {purpose === 'REGISTER'
                ? 'We have sent a 4-digit verification code to'
                : purpose === 'RESET'
                ? 'Enter the 4-digit code sent to reset your password'
                : 'Enter the 4-digit code sent to verify your account'}
            </Text>

            <Text style={[styles.email, { color: theme.colors.text }]}>
              {email || phone || ''}
            </Text>

            <Animated.View
              style={[
                styles.otpContainer,
                { transform: [{ translateX: shakeAnimation }] }
              ]}
            >
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={ref => (inputRefs.current[index] = ref)}
                  style={[
                    styles.otpInput,
                    {
                      borderColor: verificationError
                        ? theme.colors.error
                        : digit
                          ? theme.colors.primary
                          : theme.colors.border,
                      color: theme.colors.text,
                      backgroundColor: theme.colors.card
                    }
                  ]}
                  value={digit}
                  onChangeText={text => handleOtpChange(text, index)}
                  keyboardType="number-pad"
                  maxLength={1}
                  onKeyPress={({ nativeEvent }) => {
                    if (nativeEvent.key === 'Backspace' && !digit && index > 0) {
                      inputRefs.current[index - 1]?.focus();
                    }
                  }}
                  accessibilityLabel={`OTP digit ${index + 1}`}
                  editable={!isLoading && !verificationSuccess}
                />
              ))}
            </Animated.View>

            {/* Error message */}
            {verificationError ? (
              <Text style={[
                styles.errorText,
                {
                  color: verificationError.includes('sent to your email')
                    ? theme.colors.success
                    : theme.colors.error
                }
              ]}>
                {verificationError}
              </Text>
            ) : null}

            <Button
              title="Verify"
              onPress={() => handleVerifyOtp()}
              loading={isLoading}
              style={styles.verifyButton}
              disabled={isLoading || verificationSuccess || otp.join('').length !== 4}
              variant="warning"
              accessibilityLabel="Verify OTP code"
            />

            <View style={styles.resendContainer}>
              <Text style={[styles.resendText, { color: theme.colors.textLight }]}>
                Didn't receive the code?
              </Text>
              {canResend ? (
                <TouchableOpacity
                  onPress={handleResendOtp}
                  disabled={isLoading || verificationSuccess}
                  accessibilityLabel="Resend verification code"
                >
                  <Text style={[
                    styles.resendButton,
                    {
                      color: theme.colors.primary,
                      opacity: (isLoading || verificationSuccess) ? 0.5 : 1
                    }
                  ]}>
                    Resend
                  </Text>
                </TouchableOpacity>
              ) : (
                <Text style={[styles.timer, { color: theme.colors.textLight }]}>
                  Resend in {timeLeft}s
                </Text>
              )}
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.9,
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 10,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 16,
    position: 'relative',
  },
  successOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    borderRadius: 16,
  },
  successIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  successText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    textAlign: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 8,
    textAlign: 'center',
    lineHeight: 22,
  },
  email: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 32,
    textAlign: 'center',
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 20,
  },
  otpInput: {
    width: 60,
    height: 60,
    borderWidth: 2,
    borderRadius: 12,
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
    width: '100%',
  },
  verifyButton: {
    width: '100%',
    marginBottom: 24,
    height: 50,
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    paddingVertical: 8,
  },
  resendText: {
    fontSize: 14,
    marginRight: 5,
  },
  resendButton: {
    fontSize: 14,
    fontWeight: 'bold',
    padding: 4,
  },
  timer: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default OtpVerificationModal;
