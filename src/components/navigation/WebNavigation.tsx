"use client"

import type React from "react"
import { View, Text, StyleSheet, TouchableOpacity } from "react-native"
import { useNavigation } from "@react-navigation/native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../context/ThemeContext"
import ResponsiveContainer from "../ResponsiveContainer"

interface WebNavigationProps {
  currentRoute: string
}

const WebNavigation: React.FC<WebNavigationProps> = ({ currentRoute }) => {
  const navigation = useNavigation()
  const theme = useTheme()

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 16,
    },
    content: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    logoContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    logoText: {
      color: "white",
      fontSize: 20,
      fontWeight: "700",
      marginLeft: 8,
    },
    navLinks: {
      flexDirection: "row",
    },
    navLink: {
      marginLeft: 24,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 4,
    },
    activeNavLink: {
      backgroundColor: "rgba(255, 255, 255, 0.2)",
    },
    navLinkText: {
      color: "white",
      fontSize: 16,
    },
    rightSection: {
      flexDirection: "row",
      alignItems: "center",
    },
    iconButton: {
      marginLeft: 16,
      padding: 8,
    },
    profileButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: "rgba(255, 255, 255, 0.2)",
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 4,
      marginLeft: 16,
    },
    profileText: {
      color: "white",
      marginLeft: 8,
    },
  })

  const navLinks = [
    { name: "Home", route: "Home" },
    { name: "Services", route: "ServiceBrowsing" },
    { name: "Bookings", route: "BookingHistory" },
    { name: "About", route: "About" },
    { name: "Contact", route: "Contact" },
  ]

  return (
    <View style={styles.container}>
      <ResponsiveContainer>
        <View style={styles.content}>
          <View style={styles.logoContainer}>
            <Feather name="home" size={24} color="white" />
            <Text style={styles.logoText}>CleanConnect</Text>
          </View>

          <View style={styles.navLinks}>
            {navLinks.map((link) => (
              <TouchableOpacity
                key={link.route}
                style={[styles.navLink, currentRoute === link.route && styles.activeNavLink]}
                onPress={() => navigation.navigate(link.route)}
              >
                <Text style={styles.navLinkText}>{link.name}</Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.rightSection}>
            <TouchableOpacity style={styles.iconButton}>
              <Feather name="search" size={20} color="white" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.iconButton}>
              <Feather name="bell" size={20} color="white" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.profileButton} onPress={() => navigation.navigate("Profile")}>
              <Feather name="user" size={20} color="white" />
              <Text style={styles.profileText}>My Account</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ResponsiveContainer>
    </View>
  )
}

export default WebNavigation
