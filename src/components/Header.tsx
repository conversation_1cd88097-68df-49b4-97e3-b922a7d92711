"use client";

import type React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Feather } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { useTheme } from "../context/ThemeContext";
import { SafeAreaView, useSafeAreaInsets } from "react-native-safe-area-context";

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  rightIcon?: React.ReactNode;
  rightComponent?: React.ReactNode;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showBackButton = false,
  rightIcon,
  rightComponent,
}) => {
  const navigation = useNavigation();
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const styles = StyleSheet.create({
    backButton: {
      marginRight: theme.spacing.sm,
    },
    container: {
      alignItems: "center",
      backgroundColor: theme.colors.primary,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
    },
    leftContainer: {
      alignItems: "center",
      flexDirection: "row",
    },
    title: {
      color: "white",
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginLeft: showBackButton ? theme.spacing.sm : 0,
    },
  });

  return (
    <View style={{ backgroundColor: theme.colors.primary }}>
      {/* Only add padding for the status bar height, not the entire safe area */}
      <View style={{ height: insets.top, backgroundColor: theme.colors.primary }} />
      <View style={styles.container}>
        <View style={styles.leftContainer}>
          {showBackButton && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Feather name="arrow-left" size={24} color="white" />
            </TouchableOpacity>
          )}
          <Text style={styles.title}>{title}</Text>
        </View>
        {rightComponent || rightIcon}
      </View>
    </View>
  );
};

export default Header;
