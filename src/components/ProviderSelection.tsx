import React from "react";
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView } from "react-native";
import { Feather } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import Card from "./Card";
import type { Provider } from "../data/providers";

interface ProviderSelectionProps {
  providers: Provider[];
  selectedProviderId: string | null;
  onSelectProvider: (providerId: string | null) => void;
  autoAssignEnabled: boolean;
  onToggleAutoAssign: () => void;
}

const ProviderSelection: React.FC<ProviderSelectionProps> = ({
  providers,
  selectedProviderId,
  onSelectProvider,
  autoAssignEnabled,
  onToggleAutoAssign,
}) => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      marginBottom: theme.spacing.lg,
    },
    title: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
    },
    autoAssignContainer: {
      borderWidth: 2,
      borderColor: autoAssignEnabled ? theme.colors.primary : theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      backgroundColor: autoAssignEnabled ? `${theme.colors.primary}10` : "white",
    },
    autoAssignContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    autoAssignIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: `${theme.colors.accent}20`,
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.md,
    },
    autoAssignInfo: {
      flex: 1,
    },
    autoAssignTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
      marginBottom: 4,
    },
    autoAssignDescription: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    providersContainer: {
      marginTop: theme.spacing.md,
    },
    providerCard: {
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      backgroundColor: "white",
    },
    providerCardSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}10`,
    },
    providerContent: {
      flexDirection: "row",
      alignItems: "center",
    },
    providerImage: {
      width: 50,
      height: 50,
      borderRadius: 25,
      marginRight: theme.spacing.md,
    },
    providerInfo: {
      flex: 1,
    },
    providerName: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
      marginBottom: 2,
    },
    providerStats: {
      flexDirection: "row",
      alignItems: "center",
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginRight: theme.spacing.sm,
    },
    star: {
      color: "#FFD700",
      marginRight: 2,
    },
    ratingText: {
      fontSize: theme.fontSizes.xs,
      marginRight: 2,
    },
    jobsText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.textLight,
    },
    verifiedBadge: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: `${theme.colors.success}20`,
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
      marginLeft: theme.spacing.sm,
    },
    verifiedText: {
      fontSize: theme.fontSizes.xs,
      color: theme.colors.success,
      marginLeft: 2,
    },
    noProvidersContainer: {
      padding: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
    },
    noProvidersText: {
      fontSize: theme.fontSizes.md,
      color: theme.colors.textLight,
      textAlign: "center",
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Choose a Provider</Text>
      
      {/* Auto-assign option */}
      <TouchableOpacity
        style={[styles.autoAssignContainer]}
        onPress={onToggleAutoAssign}
      >
        <View style={styles.autoAssignContent}>
          <View style={styles.autoAssignIconContainer}>
            <Feather name="shuffle" size={20} color={theme.colors.accent} />
          </View>
          <View style={styles.autoAssignInfo}>
            <Text style={styles.autoAssignTitle}>Let CleanConnect decide</Text>
            <Text style={styles.autoAssignDescription}>
              We'll assign the first available qualified provider
            </Text>
          </View>
          {autoAssignEnabled && <Feather name="check" size={20} color={theme.colors.primary} />}
        </View>
      </TouchableOpacity>

      {/* Provider list */}
      {!autoAssignEnabled && (
        <View style={styles.providersContainer}>
          {providers.length > 0 ? (
            providers.map((provider) => (
              <TouchableOpacity
                key={provider.id}
                style={[
                  styles.providerCard,
                  selectedProviderId === provider.id && styles.providerCardSelected,
                ]}
                onPress={() => onSelectProvider(provider.id)}
              >
                <View style={styles.providerContent}>
                  <Image source={{ uri: provider.image }} style={styles.providerImage} />
                  <View style={styles.providerInfo}>
                    <Text style={styles.providerName}>{provider.name}</Text>
                    <View style={styles.providerStats}>
                      <View style={styles.ratingContainer}>
                        <Feather name="star" size={12} style={styles.star} />
                        <Text style={styles.ratingText}>{provider.rating}</Text>
                      </View>
                      <Text style={styles.jobsText}>({provider.jobs} jobs)</Text>
                      {provider.verified && (
                        <View style={styles.verifiedBadge}>
                          <Feather name="check" size={10} color={theme.colors.success} />
                          <Text style={styles.verifiedText}>Verified</Text>
                        </View>
                      )}
                    </View>
                  </View>
                  {selectedProviderId === provider.id && (
                    <Feather name="check" size={20} color={theme.colors.primary} />
                  )}
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.noProvidersContainer}>
              <Text style={styles.noProvidersText}>
                No providers available for the selected time and service.
                Please try a different time or date.
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

export default ProviderSelection;
