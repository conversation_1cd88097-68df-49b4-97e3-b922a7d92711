import type React from "react"
import { Image, type ImageStyle, type StyleProp, StyleSheet } from "react-native"
import { useResponsive } from "../hooks/useResponsive"

interface ResponsiveImageProps {
  source: any
  style?: StyleProp<ImageStyle>
  webStyle?: StyleProp<ImageStyle>
  mobileStyle?: StyleProp<ImageStyle>
  resizeMode?: "cover" | "contain" | "stretch" | "repeat" | "center"
  alt?: string // For web accessibility
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  source,
  style,
  webStyle,
  mobileStyle,
  resizeMode = "cover",
  alt = "",
}) => {
  const { isWeb, width } = useResponsive()

  // Calculate responsive dimensions based on container width
  const getResponsiveStyle = () => {
    if (isWeb) {
      return webStyle
    }
    return mobileStyle
  }

  const styles = StyleSheet.create({
    image: {
      width: "100%",
      height: undefined,
      aspectRatio: 16 / 9, // Default aspect ratio
    },
  })

  // For web, we can add accessibility attributes
  const webProps = isWeb
    ? {
        accessibilityLabel: alt,
        alt,
      }
    : {}

  return (
    <Image source={source} style={[styles.image, style, getResponsiveStyle()]} resizeMode={resizeMode} {...webProps} />
  )
}

export default ResponsiveImage
