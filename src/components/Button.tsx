import type React from "react"
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  type StyleProp,
  type ViewStyle,
  type TextStyle,
  View,
} from "react-native"
import { useTheme } from "../context/ThemeContext"

interface ButtonProps {
  title: string
  onPress: () => void
  variant?: "primary" | "secondary" | "outline" | "warning"
  size?: "small" | "medium" | "large"
  disabled?: boolean
  loading?: boolean
  fullWidth?: boolean
  style?: StyleProp<ViewStyle>
  textStyle?: StyleProp<TextStyle>
  icon?: React.ReactNode
  accessibilityLabel?: string
  accessibilityHint?: string
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = "primary",
  size = "medium",
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  icon,
  accessibilityLabel,
  accessibilityHint,
}) => {
  const theme = useTheme()

  const getBackgroundColor = () => {
    if (disabled) return theme.colors.border

    switch (variant) {
      case "primary":
        return theme.colors.primary
      case "secondary":
        return theme.colors.secondary
      case "warning":
        return theme.colors.warning
      case "outline":
        return "transparent"
      default:
        return theme.colors.primary
    }
  }

  const getTextColor = () => {
    if (disabled) return theme.colors.textLight

    switch (variant) {
      case "primary":
      case "secondary":
        return "white"
      case "warning":
        return "black"
      case "outline":
        return theme.colors.primary
      default:
        return "white"
    }
  }

  const getPadding = () => {
    switch (size) {
      case "small":
        return { paddingVertical: 8, paddingHorizontal: 12 }
      case "medium":
        return { paddingVertical: 12, paddingHorizontal: 16 }
      case "large":
        return { paddingVertical: 16, paddingHorizontal: 24 }
      default:
        return { paddingVertical: 12, paddingHorizontal: 16 }
    }
  }

  const getBorderStyle = () => {
    return variant === "outline" ? { borderWidth: 1, borderColor: theme.colors.primary } : {}
  }

  const styles = StyleSheet.create({
    button: {
      backgroundColor: getBackgroundColor(),
      borderRadius: theme.borderRadius.md,
      ...getPadding(),
      alignItems: "center",
      justifyContent: "center",
      flexDirection: "row",
      ...getBorderStyle(),
      width: fullWidth ? "100%" : "auto",
    },
    text: {
      color: getTextColor(),
      fontWeight: "600",
      fontSize: size === "small" ? 14 : size === "large" ? 18 : 16,
    },
    iconContainer: {
      marginRight: 8,
    },
  })

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityRole="button"
      accessibilityState={{ disabled: disabled || loading }}
    >
      {loading ? (
        <ActivityIndicator size="small" color={getTextColor()} />
      ) : (
        <>
          {icon && <View style={styles.iconContainer}>{icon}</View>}
          <Text style={[styles.text, textStyle]}>{title}</Text>
        </>
      )}
    </TouchableOpacity>
  )
}

export default Button
