"use client"

import type React from "react"
import { Text, type TextStyle, type StyleProp, StyleSheet } from "react-native"
import { useResponsive } from "../hooks/useResponsive"
import { useTheme } from "../hooks/useTheme"

interface ResponsiveTextProps {
  children: React.ReactNode
  style?: StyleProp<TextStyle>
  webStyle?: StyleProp<TextStyle>
  mobileStyle?: StyleProp<TextStyle>
  variant?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "body" | "caption" | "button"
  numberOfLines?: number
}

const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  style,
  webStyle,
  mobileStyle,
  variant = "body",
  numberOfLines,
  ...props
}) => {
  const { isWeb, deviceType } = useResponsive()
  const { theme } = useTheme()

  // Define font sizes based on variant and device type
  const getFontSize = () => {
    const baseSizes = {
      h1: { mobile: 24, tablet: 28, desktop: 32 },
      h2: { mobile: 20, tablet: 24, desktop: 28 },
      h3: { mobile: 18, tablet: 20, desktop: 24 },
      h4: { mobile: 16, tablet: 18, desktop: 20 },
      h5: { mobile: 14, tablet: 16, desktop: 18 },
      h6: { mobile: 12, tablet: 14, desktop: 16 },
      body: { mobile: 14, tablet: 16, desktop: 16 },
      caption: { mobile: 12, tablet: 12, desktop: 14 },
      button: { mobile: 14, tablet: 16, desktop: 16 },
    }

    return baseSizes[variant][deviceType]
  }

  // Define font weights based on variant
  const getFontWeight = () => {
    switch (variant) {
      case "h1":
      case "h2":
      case "h3":
        return "700" // bold
      case "h4":
      case "h5":
      case "h6":
      case "button":
        return "600" // semi-bold
      default:
        return "400" // normal
    }
  }

  const styles = StyleSheet.create({
    text: {
      fontSize: getFontSize(),
      fontWeight: getFontWeight() as TextStyle["fontWeight"],
      color: theme.colors.text,
    },
  })

  return (
    <Text style={[styles.text, style, isWeb ? webStyle : mobileStyle]} numberOfLines={numberOfLines} {...props}>
      {children}
    </Text>
  )
}

export default ResponsiveText
