import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Modal,
  Animated,
  Dimensions,
  Alert,
  ActivityIndicator,
  Keyboard,
  FlatList,
  Pressable,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useLocation } from '../context/LocationContext';

// Helper function to extract a detailed area name from an address (neighborhood-city format)
const extractAreaName = (address: string): string => {
  if (!address) return '';

  // Split the address by commas
  const parts = address.split(',').map(part => part.trim());

  // Find parts without numbers (likely neighborhood, area, city names)
  const cleanParts = parts.filter(part => part && !/\d/.test(part));

  if (cleanParts.length >= 2) {
    // If we have at least two clean parts (like neighborhood and city)
    // Format as "Neighborhood-City"
    return `${cleanParts[0]}-${cleanParts[1]}`;
  } else if (cleanParts.length === 1) {
    // If we only have one clean part
    return cleanParts[0];
  }

  // If all parts contain numbers, return the first part
  return parts[0] || '';
};

interface LocationSelectorBottomSheetProps {
  visible: boolean;
  onClose: () => void;
}

type AddressLabelType = 'Home' | 'Work' | 'Other';

interface AddressFormData {
  label: AddressLabelType;
  address: string;
  area: string;
  isPrimary: boolean;
}

const INITIAL_FORM_DATA: AddressFormData = {
  label: 'Home',
  address: '',
  area: '',
  isPrimary: false,
};

const LocationSelectorBottomSheet: React.FC<LocationSelectorBottomSheetProps> = ({
  visible,
  onClose,
}) => {
  const theme = useTheme();
  const { height, width } = Dimensions.get('window');
  const {
    currentLocation,
    savedLocations,
    setCurrentLocation,
    addLocation,
    removeLocation,
    isLoading: isContextLoading
  } = useLocation();

  // Local state
  const [searchText, setSearchText] = useState('');
  const [plusCode, setPlusCode] = useState('');
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [formData, setFormData] = useState<AddressFormData>(INITIAL_FORM_DATA);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Animation values
  const slideAnim = useRef(new Animated.Value(height)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;
  const searchInputRef = useRef<TextInput>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  // Keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Animation functions
  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Reset state when closing
      setTimeout(() => {
        setSearchText('');
        setPlusCode('');
        setShowAddressForm(false);
        setFormData(INITIAL_FORM_DATA);
        setIsSubmitting(false);
        setIsSearchFocused(false);
      }, 300);
    }
  }, [visible, slideAnim, backdropOpacity, height]);

  // Filter locations based on search
  const filteredLocations = useMemo(() => {
    if (!searchText.trim()) return savedLocations;

    const searchLower = searchText.toLowerCase();
    return savedLocations.filter(
      loc =>
        loc.label.toLowerCase().includes(searchLower) ||
        loc.address.toLowerCase().includes(searchLower) ||
        (loc.area && loc.area.toLowerCase().includes(searchLower))
    );
  }, [savedLocations, searchText]);

  // Handle use current location
  const handleUseCurrentLocation = useCallback(() => {
    // In a real app, this would use the device's geolocation API
    Alert.alert(
      "Use Current Location",
      "This would open a map view to select your precise location using GPS.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Simulate Selection",
          onPress: async () => {
            try {
              // Simulate getting location from GPS
              const simulatedLocation = {
                id: `current_${Date.now()}`,
                label: 'Current Location',
                address: 'Serrekunda, Bundung Highway',
                area: 'Serrekunda',
                coordinates: {
                  latitude: 13.4549,
                  longitude: -16.6917
                }
              };

              await setCurrentLocation(simulatedLocation);

              // Show success feedback
              Alert.alert(
                "Location Updated",
                "Your current location has been set successfully.",
                [{ text: "OK", onPress: onClose }]
              );
            } catch (error) {
              Alert.alert("Error", "Failed to set current location. Please try again.");
            }
          }
        }
      ]
    );
  }, [setCurrentLocation, onClose]);

  // Handle plus code search
  const handlePlusCodeSearch = useCallback(() => {
    if (!plusCode.trim()) {
      Alert.alert("Error", "Please enter a Plus Code");
      return;
    }

    // In a real app, this would validate and geocode the Plus Code
    Alert.alert(
      "Plus Code Search",
      `This would search for location using Plus Code: ${plusCode}`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Simulate Result",
          onPress: async () => {
            try {
              const simulatedLocation = {
                id: `pluscode_${Date.now()}`,
                label: 'Plus Code Location',
                address: `Location from Plus Code: ${plusCode}`,
                area: 'Banjul',
                coordinates: {
                  latitude: 13.4557,
                  longitude: -16.5785
                }
              };

              await setCurrentLocation(simulatedLocation);
              setPlusCode('');

              // Show success feedback
              Alert.alert(
                "Location Updated",
                "Your location has been set successfully using the Plus Code.",
                [{ text: "OK", onPress: onClose }]
              );
            } catch (error) {
              Alert.alert("Error", "Failed to set location. Please try again.");
            }
          }
        }
      ]
    );
  }, [plusCode, setCurrentLocation, onClose]);

  // Handle select saved location
  const handleSelectLocation = useCallback(async (location) => {
    try {
      await setCurrentLocation(location);
      onClose();
    } catch (error) {
      Alert.alert("Error", "Failed to set location. Please try again.");
    }
  }, [setCurrentLocation, onClose]);

  // Handle form input changes
  const handleFormInputChange = useCallback((field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  // Handle save new address
  const handleSaveNewAddress = useCallback(async () => {
    // Validate form
    if (!formData.address.trim()) {
      Alert.alert("Error", "Please enter an address");
      return;
    }

    try {
      setIsSubmitting(true);

      const newLocation = {
        id: `manual_${Date.now()}`,
        label: formData.label,
        address: formData.address,
        area: formData.area,
        isPrimary: formData.isPrimary
      };

      await addLocation(newLocation);

      // Reset form
      setFormData(INITIAL_FORM_DATA);
      setShowAddressForm(false);

      // Show success feedback
      Alert.alert(
        "Address Added",
        "Your new address has been added successfully.",
        [{ text: "OK", onPress: onClose }]
      );
    } catch (error) {
      Alert.alert("Error", "Failed to add address. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, addLocation, onClose]);

  // Handle delete location
  const handleDeleteLocation = useCallback((locationId) => {
    Alert.alert(
      "Delete Address",
      "Are you sure you want to delete this address?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await removeLocation(locationId);
            } catch (error) {
              if (error instanceof Error && error.message === 'Cannot remove the only location') {
                Alert.alert("Error", "You cannot delete your only address.");
              } else {
                Alert.alert("Error", "Failed to delete address. Please try again.");
              }
            }
          }
        }
      ]
    );
  }, [removeLocation]);

  // Render location item
  const renderLocationItem = useCallback(({ item }) => {
    const isSelected = currentLocation?.id === item.id;

    return (
      <Pressable
        style={[
          styles.addressItem,
          { borderColor: theme.colors.border },
          isSelected && {
            backgroundColor: `${theme.colors.primary}10`,
            borderColor: theme.colors.primary,
          }
        ]}
        onPress={() => handleSelectLocation(item)}
        android_ripple={{ color: `${theme.colors.primary}20` }}
      >
        <View style={styles.addressIcon}>
          <Feather
            name={item.label === 'Home' ? 'home' : item.label === 'Work' ? 'briefcase' : 'map-pin'}
            size={20}
            color={theme.colors.primary}
          />
        </View>
        <View style={styles.addressInfo}>
          <Text style={[styles.addressLabel, { color: theme.colors.text }]}>
            {isSelected && <Text style={{ color: theme.colors.primary }}>Current: </Text>}
            {item.label}
          </Text>
          <Text style={[styles.addressText, { color: theme.colors.textLight }]} numberOfLines={1}>
            {(item.area && item.area.includes('-')) ? item.area : extractAreaName(item.address)}
          </Text>
        </View>
        <View style={styles.addressActions}>
          {isSelected && (
            <Feather name="check" size={20} color={theme.colors.primary} style={styles.checkIcon} />
          )}
          {savedLocations.length > 1 && (
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteLocation(item.id)}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <Feather name="trash-2" size={18} color={theme.colors.error} />
            </TouchableOpacity>
          )}
        </View>
      </Pressable>
    );
  }, [currentLocation, savedLocations, theme, handleSelectLocation, handleDeleteLocation]);

  // Calculate bottom sheet height based on content and keyboard
  const bottomSheetHeight = useMemo(() => {
    if (showAddressForm) {
      return keyboardVisible ? '90%' : '70%';
    }
    return keyboardVisible ? '80%' : '60%';
  }, [showAddressForm, keyboardVisible]);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <Pressable
          style={StyleSheet.absoluteFill}
          onPress={onClose}
        >
          <Animated.View
            style={[
              StyleSheet.absoluteFill,
              styles.backdrop,
              { opacity: backdropOpacity }
            ]}
          />
        </Pressable>

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={styles.keyboardAvoidingView}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
        >
          <Animated.View
            style={[
              styles.bottomSheet,
              {
                transform: [{ translateY: slideAnim }],
                backgroundColor: theme.colors.card,
                height: bottomSheetHeight,
                maxHeight: '90%',
              }
            ]}
          >
            {/* Handle */}
            <View style={styles.handleContainer}>
              <View style={[styles.handle, { backgroundColor: theme.colors.border }]} />
            </View>

            {/* Header */}
            <View style={styles.header}>
              <Text style={[styles.title, { color: theme.colors.text }]}>Select Location</Text>
              <TouchableOpacity
                onPress={onClose}
                style={styles.closeButton}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Feather name="x" size={24} color={theme.colors.text} />
              </TouchableOpacity>
            </View>

            {isContextLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text style={[styles.loadingText, { color: theme.colors.text }]}>
                  Loading locations...
                </Text>
              </View>
            ) : showAddressForm ? (
              // Add Address Form
              <ScrollView
                style={styles.formScrollView}
                contentContainerStyle={styles.formContainer}
                keyboardShouldPersistTaps="handled"
                ref={scrollViewRef}
              >
                <Text style={[styles.formLabel, { color: theme.colors.text }]}>Address Label</Text>
                <View style={styles.labelTypeContainer}>
                  {(['Home', 'Work', 'Other'] as const).map((label) => (
                    <TouchableOpacity
                      key={label}
                      style={[
                        styles.labelTypeButton,
                        { borderColor: theme.colors.border },
                        formData.label === label && [
                          styles.selectedLabelType,
                          { backgroundColor: `${theme.colors.primary}20`, borderColor: theme.colors.primary }
                        ]
                      ]}
                      onPress={() => handleFormInputChange('label', label)}
                    >
                      <Feather
                        name={label === 'Home' ? 'home' : label === 'Work' ? 'briefcase' : 'map-pin'}
                        size={16}
                        color={formData.label === label ? theme.colors.primary : theme.colors.textLight}
                        style={styles.labelTypeIcon}
                      />
                      <Text
                        style={[
                          styles.labelTypeText,
                          { color: formData.label === label ? theme.colors.primary : theme.colors.textLight }
                        ]}
                      >
                        {label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                <Text style={[styles.formLabel, { color: theme.colors.text }]}>Address</Text>
                <TextInput
                  style={[styles.formInput, { borderColor: theme.colors.border, color: theme.colors.text }]}
                  placeholder="Enter your address"
                  placeholderTextColor={theme.colors.textLight}
                  value={formData.address}
                  onChangeText={(text) => handleFormInputChange('address', text)}
                  multiline
                  autoCapitalize="words"
                />

                <Text style={[styles.formLabel, { color: theme.colors.text }]}>City / Area</Text>
                <TextInput
                  style={[styles.formInput, { borderColor: theme.colors.border, color: theme.colors.text }]}
                  placeholder="Enter city or area"
                  placeholderTextColor={theme.colors.textLight}
                  value={formData.area}
                  onChangeText={(text) => handleFormInputChange('area', text)}
                  autoCapitalize="words"
                />

                <View style={styles.primaryContainer}>
                  <TouchableOpacity
                    style={styles.checkboxContainer}
                    onPress={() => handleFormInputChange('isPrimary', !formData.isPrimary)}
                  >
                    <View style={[
                      styles.checkbox,
                      { borderColor: theme.colors.border },
                      formData.isPrimary && { backgroundColor: theme.colors.primary, borderColor: theme.colors.primary }
                    ]}>
                      {formData.isPrimary && <Feather name="check" size={14} color="white" />}
                    </View>
                    <Text style={[styles.checkboxLabel, { color: theme.colors.text }]}>
                      Set as primary address
                    </Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.formButtons}>
                  <TouchableOpacity
                    style={[styles.formButton, styles.cancelButton, { borderColor: theme.colors.border }]}
                    onPress={() => setShowAddressForm(false)}
                    disabled={isSubmitting}
                  >
                    <Text style={[styles.cancelButtonText, { color: theme.colors.text }]}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.formButton,
                      styles.saveButton,
                      { backgroundColor: theme.colors.primary },
                      isSubmitting && { opacity: 0.7 }
                    ]}
                    onPress={handleSaveNewAddress}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <ActivityIndicator size="small" color="white" />
                    ) : (
                      <Text style={styles.saveButtonText}>Save</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </ScrollView>
            ) : (
              <>
                {/* Search Bar */}
                <View style={[styles.searchContainer, { borderColor: theme.colors.border }]}>
                  <Feather name="search" size={20} color={theme.colors.textLight} style={styles.searchIcon} />
                  <TextInput
                    ref={searchInputRef}
                    style={[styles.searchInput, { color: theme.colors.text }]}
                    placeholder="Search for an address..."
                    placeholderTextColor={theme.colors.textLight}
                    value={searchText}
                    onChangeText={setSearchText}
                    onFocus={() => setIsSearchFocused(true)}
                    onBlur={() => setIsSearchFocused(false)}
                    returnKeyType="search"
                    clearButtonMode="while-editing"
                  />
                </View>

                {/* Plus Code Input */}
                <View style={styles.plusCodeContainer}>
                  <View style={[styles.plusCodeInputContainer, { borderColor: theme.colors.border }]}>
                    <TextInput
                      style={[styles.plusCodeInput, { color: theme.colors.text }]}
                      placeholder="Enter Google Plus Code"
                      placeholderTextColor={theme.colors.textLight}
                      value={plusCode}
                      onChangeText={setPlusCode}
                      returnKeyType="search"
                      onSubmitEditing={handlePlusCodeSearch}
                    />
                    <TouchableOpacity
                      style={[styles.plusCodeButton, { backgroundColor: theme.colors.primary }]}
                      onPress={handlePlusCodeSearch}
                      disabled={!plusCode.trim()}
                    >
                      <Feather name="search" size={18} color="white" />
                    </TouchableOpacity>
                  </View>
                  <TouchableOpacity
                    onPress={() => Alert.alert(
                      "Google Plus Code",
                      "A Plus Code is a short code for your address that can be used to find locations where street addresses don't exist. Find your Plus Code on Google Maps."
                    )}
                    hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                  >
                    <Text style={[styles.plusCodeHelp, { color: theme.colors.primary }]}>
                      What is a Plus Code?
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* Current Location Button */}
                <TouchableOpacity
                  style={[styles.actionButton, { borderColor: theme.colors.border }]}
                  onPress={handleUseCurrentLocation}
                  activeOpacity={0.7}
                >
                  <Feather name="map-pin" size={20} color={theme.colors.primary} style={styles.buttonIcon} />
                  <Text style={[styles.buttonText, { color: theme.colors.text }]}>Use Current Location</Text>
                </TouchableOpacity>

                {/* Add Address Manually Button */}
                <TouchableOpacity
                  style={[styles.actionButton, { borderColor: theme.colors.border }]}
                  onPress={() => setShowAddressForm(true)}
                  activeOpacity={0.7}
                >
                  <Feather name="plus" size={20} color={theme.colors.primary} style={styles.buttonIcon} />
                  <Text style={[styles.buttonText, { color: theme.colors.text }]}>Add Address Manually</Text>
                </TouchableOpacity>

                {/* Saved Addresses */}
                <View style={styles.savedAddressesContainer}>
                  <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                    Saved Addresses
                  </Text>

                  {filteredLocations.length === 0 ? (
                    <View style={styles.emptyContainer}>
                      {searchText ? (
                        <>
                          <Feather name="search" size={24} color={theme.colors.textLight} />
                          <Text style={[styles.emptyText, { color: theme.colors.textLight }]}>
                            No addresses match your search
                          </Text>
                        </>
                      ) : (
                        <>
                          <Feather name="map-pin" size={24} color={theme.colors.textLight} />
                          <Text style={[styles.emptyText, { color: theme.colors.textLight }]}>
                            No saved addresses
                          </Text>
                        </>
                      )}
                    </View>
                  ) : (
                    <FlatList
                      data={filteredLocations}
                      renderItem={renderLocationItem}
                      keyExtractor={(item) => item.id}
                      style={styles.addressList}
                      contentContainerStyle={styles.addressListContent}
                      showsVerticalScrollIndicator={false}
                      keyboardShouldPersistTaps="handled"
                    />
                  )}
                </View>
              </>
            )}
          </Animated.View>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    backgroundColor: '#000',
  },
  keyboardAvoidingView: {
    width: '100%',
  },
  bottomSheet: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  handle: {
    width: 40,
    height: 5,
    borderRadius: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 45,
    marginBottom: 15,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  plusCodeContainer: {
    marginBottom: 15,
  },
  plusCodeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    overflow: 'hidden',
    height: 45,
  },
  plusCodeInput: {
    flex: 1,
    height: '100%',
    paddingHorizontal: 10,
    fontSize: 16,
  },
  plusCodeButton: {
    height: '100%',
    paddingHorizontal: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  plusCodeHelp: {
    fontSize: 12,
    marginTop: 5,
    textAlign: 'right',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    fontSize: 16,
  },
  savedAddressesContainer: {
    flex: 1,
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  addressList: {
    flex: 1,
  },
  addressListContent: {
    paddingBottom: 20,
  },
  addressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
  },
  addressIcon: {
    marginRight: 10,
  },
  addressInfo: {
    flex: 1,
  },
  addressLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  addressText: {
    fontSize: 14,
  },
  addressArea: {
    fontSize: 12,
    marginTop: 2,
  },
  addressDetails: {
    fontSize: 12,
    marginTop: 2,
    opacity: 0.8,
  },
  addressActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkIcon: {
    marginRight: 10,
  },
  deleteButton: {
    padding: 5,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 14,
  },
  formScrollView: {
    flex: 1,
  },
  formContainer: {
    paddingTop: 10,
    paddingBottom: 20,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  labelTypeContainer: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  labelTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 10,
  },
  selectedLabelType: {
    borderWidth: 1,
  },
  labelTypeIcon: {
    marginRight: 5,
  },
  labelTypeText: {
    fontSize: 14,
  },
  formInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    marginBottom: 15,
    minHeight: 45,
  },
  primaryContainer: {
    marginBottom: 20,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderRadius: 4,
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: 14,
  },
  formButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  formButton: {
    flex: 1,
    height: 45,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
    marginRight: 10,
  },
  saveButton: {
    marginLeft: 10,
  },
  cancelButtonText: {
    fontSize: 16,
  },
  saveButtonText: {
    fontSize: 16,
    color: 'white',
    fontWeight: '500',
  },
});

export default LocationSelectorBottomSheet;
