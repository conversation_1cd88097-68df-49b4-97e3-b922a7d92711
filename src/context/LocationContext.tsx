import React, { createContext, useState, useContext, useEffect } from 'react';
import Storage from '../utils/storage';
import { useAuth } from './AuthContext';
import { getUserAddresses, createAddress as apiCreateAddress, updateAddress as apiUpdateAddress, deleteAddress as apiDeleteAddress } from '../services/addressService';
import { STORAGE_KEYS, DEFAULT_LOCATION } from '../config/constants';

export interface Location {
  id: string;
  label: string;
  address: string;
  area?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isPrimary?: boolean;
}

interface LocationContextType {
  currentLocation: Location | null;
  savedLocations: Location[];
  setCurrentLocation: (location: Location) => Promise<void>;
  addLocation: (location: Location) => Promise<void>;
  removeLocation: (locationId: string) => Promise<void>;
  updateLocation: (location: Location) => Promise<void>;
  isLoading: boolean;
}

// Default location is now imported from constants

const LocationContext = createContext<LocationContextType>({
  currentLocation: DEFAULT_LOCATION,
  savedLocations: [DEFAULT_LOCATION],
  setCurrentLocation: async () => {},
  addLocation: async () => {},
  removeLocation: async () => {},
  updateLocation: async () => {},
  isLoading: true
});

export const useLocation = () => useContext(LocationContext);

export const LocationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [currentLocation, setCurrentLocationState] = useState<Location | null>(null);
  const [savedLocations, setSavedLocations] = useState<Location[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved locations from storage or API
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setIsLoading(true);

        if (isAuthenticated) {
          // Try to get locations from API if authenticated
          try {
            console.log('Attempting to fetch addresses from API...');
            const apiLocations = await getUserAddresses();

            if (apiLocations && apiLocations.length > 0) {
              console.log(`Fetched ${apiLocations.length} addresses from API`);
              // Use API locations
              setSavedLocations(apiLocations);

              // Set current location to primary or first
              const primaryLocation = apiLocations.find(loc => loc.isPrimary) || apiLocations[0];
              setCurrentLocationState(primaryLocation);

              // Update local storage for offline use
              await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(apiLocations));
              await Storage.setItem(STORAGE_KEYS.CURRENT_LOCATION, JSON.stringify(primaryLocation));

              setIsLoading(false);
              return;
            } else {
              console.log('No addresses found in API response');
            }
          } catch (apiError: any) {
            console.error('Error fetching locations from API:', apiError);

            // If it's an authentication error, we might need to refresh the token
            if (apiError.message === 'Authentication required') {
              console.log('Authentication error detected, might need to refresh token');
              // You could trigger a token refresh here if needed
              // For now, we'll fall back to local storage
            }

            // Fall back to local storage if API fails
          }
        } else {
          console.log('User not authenticated, using local storage for locations');
        }

        // Try to get saved locations from storage
        const savedLocationsJson = await Storage.getItem(STORAGE_KEYS.SAVED_LOCATIONS);
        const currentLocationJson = await Storage.getItem(STORAGE_KEYS.CURRENT_LOCATION);

        let locations: Location[] = [];
        if (savedLocationsJson) {
          locations = JSON.parse(savedLocationsJson);
        } else {
          // If no saved locations, use default
          locations = [DEFAULT_LOCATION];
          await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(locations));
        }

        let current: Location | null = null;
        if (currentLocationJson) {
          current = JSON.parse(currentLocationJson);
        } else if (locations.length > 0) {
          // Use the first location as current if none is set
          current = locations.find(loc => loc.isPrimary) || locations[0];
          await Storage.setItem(STORAGE_KEYS.CURRENT_LOCATION, JSON.stringify(current));
        }

        setSavedLocations(locations);
        setCurrentLocationState(current);
      } catch (error) {
        console.error('Error loading locations:', error);
        // Fallback to defaults if there's an error
        setSavedLocations([DEFAULT_LOCATION]);
        setCurrentLocationState(DEFAULT_LOCATION);
      } finally {
        setIsLoading(false);
      }
    };

    loadLocations();
  }, [isAuthenticated]);

  const setCurrentLocation = async (location: Location) => {
    try {
      // Save to storage
      await Storage.setItem('currentLocation', JSON.stringify(location));
      setCurrentLocationState(location);
    } catch (error) {
      console.error('Error setting current location:', error);
      // Still update state even if storage fails
      setCurrentLocationState(location);
    }
  };

  const addLocation = async (location: Location) => {
    try {
      // Generate a unique ID if not provided
      const newLocation = {
        ...location,
        id: location.id || `loc_${Date.now()}`
      };

      // Check if this is the first location being added
      const isPrimary = savedLocations.length === 0 || location.isPrimary;

      // If this is marked as primary, remove primary flag from other locations
      let updatedLocations = [...savedLocations];
      if (isPrimary) {
        updatedLocations = updatedLocations.map(loc => ({
          ...loc,
          isPrimary: false
        }));
      }

      // Add the new location with primary flag if needed
      const locationToAdd = {
        ...newLocation,
        isPrimary: isPrimary
      };

      // If authenticated, save to API
      if (isAuthenticated) {
        try {
          // Create in API
          const apiLocation = await apiCreateAddress(locationToAdd);

          // Update local locations with API response
          updatedLocations = updatedLocations.filter(loc => loc.id !== locationToAdd.id);
          updatedLocations.push(apiLocation);
        } catch (apiError) {
          console.error('Error saving location to API:', apiError);
          // Continue with local storage even if API fails
          updatedLocations.push(locationToAdd);
        }
      } else {
        // Just add to local array if not authenticated
        updatedLocations.push(locationToAdd);
      }

      // Save to storage
      await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(updatedLocations));
      setSavedLocations(updatedLocations);

      // If this is the first location or marked as primary, set it as current
      if (isPrimary) {
        const locationToSet = updatedLocations.find(loc =>
          loc.address === locationToAdd.address && loc.isPrimary) || locationToAdd;
        await setCurrentLocation(locationToSet);
      }
    } catch (error) {
      console.error('Error adding location:', error);
      throw new Error('Failed to add location');
    }
  };

  const removeLocation = async (locationId: string) => {
    try {
      // Don't allow removing the last location
      if (savedLocations.length <= 1) {
        throw new Error('Cannot remove the only location');
      }

      // If authenticated, delete from API
      if (isAuthenticated && !locationId.startsWith('loc_') && !locationId.startsWith('gps_') && !locationId.startsWith('current_')) {
        try {
          await apiDeleteAddress(locationId);
        } catch (apiError) {
          console.error('Error deleting location from API:', apiError);
          // Continue with local deletion even if API fails
        }
      }

      const updatedLocations = savedLocations.filter(loc => loc.id !== locationId);

      // If we removed a primary location, set a new one as primary
      const removedLocation = savedLocations.find(loc => loc.id === locationId);
      if (removedLocation?.isPrimary && updatedLocations.length > 0) {
        updatedLocations[0].isPrimary = true;
      }

      // Save to storage
      await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(updatedLocations));
      setSavedLocations(updatedLocations);

      // If the current location was removed, set a new current location
      if (currentLocation?.id === locationId) {
        const newCurrent = updatedLocations.find(loc => loc.isPrimary) || updatedLocations[0];
        await setCurrentLocation(newCurrent);
      }
    } catch (error) {
      console.error('Error removing location:', error);
      throw new Error('Failed to remove location');
    }
  };

  const updateLocation = async (location: Location) => {
    try {
      const index = savedLocations.findIndex(loc => loc.id === location.id);
      if (index === -1) {
        throw new Error('Location not found');
      }

      // If this location is being set as primary, remove primary flag from others
      let updatedLocations = [...savedLocations];
      if (location.isPrimary) {
        updatedLocations = updatedLocations.map(loc => ({
          ...loc,
          isPrimary: loc.id === location.id
        }));
      } else {
        updatedLocations[index] = location;
      }

      // If authenticated and not a temporary ID, update in API
      let updatedLocation = location;
      if (isAuthenticated && !location.id.startsWith('loc_') && !location.id.startsWith('gps_') && !location.id.startsWith('current_')) {
        try {
          // Update in API
          updatedLocation = await apiUpdateAddress(location);

          // Replace with API response
          updatedLocations[index] = updatedLocation;
        } catch (apiError) {
          console.error('Error updating location in API:', apiError);
          // Continue with local update even if API fails
        }
      }

      // Save to storage
      await Storage.setItem(STORAGE_KEYS.SAVED_LOCATIONS, JSON.stringify(updatedLocations));
      setSavedLocations(updatedLocations);

      // Update current location if it's the one being updated
      if (currentLocation?.id === location.id) {
        await setCurrentLocation(updatedLocation);
      }
    } catch (error) {
      console.error('Error updating location:', error);
      throw new Error('Failed to update location');
    }
  };

  return (
    <LocationContext.Provider
      value={{
        currentLocation,
        savedLocations,
        setCurrentLocation,
        addLocation,
        removeLocation,
        updateLocation,
        isLoading
      }}
    >
      {children}
    </LocationContext.Provider>
  );
};

export default LocationContext;
