import React, { createContext, useState, useContext, useEffect } from 'react';
import Storage from '../utils/storage';
import { useAuth } from './AuthContext';

// Define the booking data interface
export interface BookingFormData {
  serviceId: string | null;
  date: string | null;
  time: string | null;
  address: string | null;
  frequency: string;
  providerId: string | null;
  autoAssign: boolean;
  notes?: string;
  lastUpdated?: number;
  serviceName?: string;
  serviceImage?: string;
  price?: number;
  duration?: string;
  // Pricing fields
  subtotal?: number;
  discountedSubtotal?: number;
  discount?: number;
  discountRate?: number;
  fee?: number;
  total?: number;
  // Payment fields
  paymentMethod?: string;
  receiptImage?: string | null;
}

// Define the abandoned booking interface for analytics
export interface AbandonedBooking {
  id: string;
  timestamp: number;
  bookingData: BookingFormData;
  userId?: string;
  completedLater?: boolean;
  completedTimestamp?: number;
}

// Define the context interface
interface BookingContextType {
  bookingData: BookingFormData;
  setBookingData: (data: Partial<BookingFormData>) => void;
  saveBookingData: () => Promise<void>;
  clearBookingData: () => Promise<void>;
  restoreBookingData: () => Promise<boolean>;
  hasStoredBooking: boolean;
  isRestoredBooking: boolean;
  setIsRestoredBooking: (value: boolean) => void;
  trackAbandonedBooking: () => Promise<void>;
  getAbandonedBookings: () => Promise<AbandonedBooking[]>;
  markAbandonedBookingAsCompleted: (id: string) => Promise<void>;
}

// Create the initial state
const initialBookingData: BookingFormData = {
  serviceId: null,
  date: null,
  time: null,
  address: null,
  frequency: 'one-time',
  providerId: null,
  autoAssign: false,
  notes: '',
  lastUpdated: undefined
};

// Create the context
const BookingContext = createContext<BookingContextType>({
  bookingData: initialBookingData,
  setBookingData: () => {},
  saveBookingData: async () => {},
  clearBookingData: async () => {},
  restoreBookingData: async () => false,
  hasStoredBooking: false,
  isRestoredBooking: false,
  setIsRestoredBooking: () => {},
  trackAbandonedBooking: async () => {},
  getAbandonedBookings: async () => [],
  markAbandonedBookingAsCompleted: async () => {}
});

// Storage keys
const BOOKING_STORAGE_KEY = 'temp_booking_data';
const ABANDONED_BOOKINGS_KEY = 'abandoned_bookings';

// Provider component
export const BookingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [bookingData, setBookingDataState] = useState<BookingFormData>(initialBookingData);
  const [hasStoredBooking, setHasStoredBooking] = useState<boolean>(false);
  const [isRestoredBooking, setIsRestoredBooking] = useState<boolean>(false);

  // Update booking data
  const setBookingData = (data: Partial<BookingFormData>) => {
    setBookingDataState(prevData => ({
      ...prevData,
      ...data,
      lastUpdated: Date.now()
    }));
  };

  // Save booking data to storage
  const saveBookingData = async () => {
    try {
      // Only save if we have at least a service ID
      if (!bookingData.serviceId) {
        console.log('Not saving booking data - no service ID');
        return false;
      }

      const dataToSave = {
        ...bookingData,
        lastUpdated: Date.now()
      };

      console.log('Saving booking data to storage:', dataToSave);

      // Stringify with proper error handling
      let jsonData;
      try {
        jsonData = JSON.stringify(dataToSave);
      } catch (jsonError) {
        console.error('Error stringifying booking data:', jsonError);
        return false;
      }

      await Storage.setItem(BOOKING_STORAGE_KEY, jsonData);
      setHasStoredBooking(true);
      console.log('Booking data saved to storage successfully');
      return true;
    } catch (error) {
      console.error('Error saving booking data:', error);
      return false;
    }
  };

  // Clear booking data from storage
  const clearBookingData = async () => {
    try {
      await Storage.removeItem(BOOKING_STORAGE_KEY);
      setBookingDataState(initialBookingData);
      setHasStoredBooking(false);
      setIsRestoredBooking(false);
      console.log('Booking data cleared from storage');
    } catch (error) {
      console.error('Error clearing booking data:', error);
    }
  };

  // Restore booking data from storage
  const restoreBookingData = async (): Promise<boolean> => {
    try {
      console.log('Attempting to restore booking data from storage');
      const storedData = await Storage.getItem(BOOKING_STORAGE_KEY);

      if (storedData) {
        console.log('Found booking data in storage');
        let parsedData: BookingFormData;

        try {
          parsedData = JSON.parse(storedData) as BookingFormData;
          console.log('Successfully parsed booking data');
        } catch (parseError) {
          console.error('Error parsing booking data:', parseError);
          await clearBookingData();
          return false;
        }

        // Check if the data is still valid (less than 24 hours old)
        const now = Date.now();
        const dataAge = now - (parsedData.lastUpdated || 0);
        const isValid = dataAge < 24 * 60 * 60 * 1000; // 24 hours

        if (isValid) {
          // Check if we have the minimum required data to continue a booking
          const hasMinimumData = parsedData.serviceId &&
            (parsedData.date || parsedData.time || parsedData.address);

          if (hasMinimumData) {
            console.log('Booking data is valid and has minimum required fields');
            setBookingDataState(parsedData);
            setIsRestoredBooking(true);
            setHasStoredBooking(true);
            console.log('Booking data restored from storage:', parsedData);
            return true;
          } else {
            console.log('Stored booking data was incomplete');
            await clearBookingData();
            return false;
          }
        } else {
          // Data is too old, clear it
          console.log('Stored booking data was too old and will be cleared');
          await clearBookingData();
          return false;
        }
      } else {
        console.log('No booking data found in storage');
      }
      return false;
    } catch (error) {
      console.error('Error restoring booking data:', error);
      return false;
    }
  };

  // Track abandoned booking for analytics
  const trackAbandonedBooking = async () => {
    try {
      // Only track if we have a service selected
      if (!bookingData.serviceId) {
        console.log('Not tracking abandoned booking - no service ID');
        return;
      }

      // Generate a unique ID for this abandoned booking
      const id = `abandoned_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      // Create the abandoned booking object
      const abandonedBooking: AbandonedBooking = {
        id,
        timestamp: Date.now(),
        bookingData: { ...bookingData },
        userId: user?.id
      };

      console.log('Tracking abandoned booking:', id);

      // Get existing abandoned bookings
      const existingData = await Storage.getItem(ABANDONED_BOOKINGS_KEY);
      let abandonedBookings: AbandonedBooking[] = [];

      if (existingData) {
        try {
          abandonedBookings = JSON.parse(existingData);
        } catch (parseError) {
          console.error('Error parsing existing abandoned bookings:', parseError);
          // Continue with an empty array if parsing fails
        }
      }

      // Add the new abandoned booking
      abandonedBookings.push(abandonedBooking);

      // Save back to storage
      try {
        const jsonData = JSON.stringify(abandonedBookings);
        await Storage.setItem(ABANDONED_BOOKINGS_KEY, jsonData);
        console.log('Abandoned booking tracked successfully:', id);
      } catch (saveError) {
        console.error('Error saving abandoned booking:', saveError);
      }
    } catch (error) {
      console.error('Error tracking abandoned booking:', error);
    }
  };

  // Get all abandoned bookings
  const getAbandonedBookings = async (): Promise<AbandonedBooking[]> => {
    try {
      const data = await Storage.getItem(ABANDONED_BOOKINGS_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error getting abandoned bookings:', error);
      return [];
    }
  };

  // Mark an abandoned booking as completed
  const markAbandonedBookingAsCompleted = async (id: string) => {
    try {
      // Get existing abandoned bookings
      const existingData = await Storage.getItem(ABANDONED_BOOKINGS_KEY);
      if (!existingData) return;

      const abandonedBookings: AbandonedBooking[] = JSON.parse(existingData);

      // Find the booking to update
      const updatedBookings = abandonedBookings.map(booking => {
        if (booking.id === id) {
          return {
            ...booking,
            completedLater: true,
            completedTimestamp: Date.now()
          };
        }
        return booking;
      });

      // Save back to storage
      await Storage.setItem(ABANDONED_BOOKINGS_KEY, JSON.stringify(updatedBookings));

      console.log('Abandoned booking marked as completed:', id);
    } catch (error) {
      console.error('Error marking abandoned booking as completed:', error);
    }
  };

  // Check for stored booking data on mount
  useEffect(() => {
    const checkStoredBooking = async () => {
      try {
        const storedData = await Storage.getItem(BOOKING_STORAGE_KEY);
        setHasStoredBooking(!!storedData);
      } catch (error) {
        console.error('Error checking stored booking:', error);
      }
    };

    checkStoredBooking();
  }, []);

  // Clear booking data when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      // Don't clear data when logging out - we want to keep it for when they log back in
      // But we should reset the restored flag
      setIsRestoredBooking(false);
    }
  }, [isAuthenticated]);

  return (
    <BookingContext.Provider
      value={{
        bookingData,
        setBookingData,
        saveBookingData,
        clearBookingData,
        restoreBookingData,
        hasStoredBooking,
        isRestoredBooking,
        setIsRestoredBooking,
        trackAbandonedBooking,
        getAbandonedBookings,
        markAbandonedBookingAsCompleted
      }}
    >
      {children}
    </BookingContext.Provider>
  );
};

// Custom hook to use the booking context
export const useBooking = () => useContext(BookingContext);
