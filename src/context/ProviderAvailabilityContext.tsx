import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { useAuth } from './AuthContext';
// Note: Availability service converted to local storage for demo

// Define the availability data interface
export interface AvailabilityData {
  id: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
}

// Define the context type
interface ProviderAvailabilityContextType {
  availabilityData: AvailabilityData[];
  isLoading: boolean;
  isRefreshing: boolean;
  isOffline: boolean;
  loadAvailability: (showFullLoading?: boolean) => Promise<void>;
  updateAvailability: (availabilityId: string, data: Partial<AvailabilityData>) => Promise<boolean>;
  createAvailability: (data: Omit<AvailabilityData, 'id'>) => Promise<boolean>;
  deleteAvailability: (availabilityId: string) => Promise<boolean>;
  handleRefresh: () => void;
}

// Create the context
const ProviderAvailabilityContext = createContext<ProviderAvailabilityContextType>({
  availabilityData: [],
  isLoading: false,
  isRefreshing: false,
  isOffline: false,
  loadAvailability: async () => {},
  updateAvailability: async () => false,
  createAvailability: async () => false,
  deleteAvailability: async () => false,
  handleRefresh: () => {},
});

// Storage key
const AVAILABILITY_STORAGE_KEY = 'provider_availability_data';

// Provider component
export const ProviderAvailabilityProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [availabilityData, setAvailabilityData] = useState<AvailabilityData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isOffline, setIsOffline] = useState<boolean>(false);

  // Load availability data
  const loadAvailability = async (showFullLoading = true) => {
    // Only load for providers
    if (!isAuthenticated || !user || user.role !== 'PROVIDER') {
      setAvailabilityData([]);
      setIsLoading(false);
      return;
    }

    try {
      if (showFullLoading) {
        setIsLoading(true);
      }
      setIsOffline(false);

      // Get availability data from local storage (demo mode)
      const cachedData = await SecureStore.getItemAsync(AVAILABILITY_STORAGE_KEY);
      let data: AvailabilityData[] = [];

      if (cachedData) {
        data = JSON.parse(cachedData);
      } else {
        // Initialize with default availability for demo
        data = [
          { id: '1', dayOfWeek: 'Monday', startTime: '08:00', endTime: '17:00', isAvailable: true },
          { id: '2', dayOfWeek: 'Tuesday', startTime: '08:00', endTime: '17:00', isAvailable: true },
          { id: '3', dayOfWeek: 'Wednesday', startTime: '08:00', endTime: '17:00', isAvailable: true },
          { id: '4', dayOfWeek: 'Thursday', startTime: '08:00', endTime: '17:00', isAvailable: true },
          { id: '5', dayOfWeek: 'Friday', startTime: '08:00', endTime: '17:00', isAvailable: true },
          { id: '6', dayOfWeek: 'Saturday', startTime: '09:00', endTime: '15:00', isAvailable: true },
          { id: '7', dayOfWeek: 'Sunday', startTime: '10:00', endTime: '14:00', isAvailable: false },
        ];
        await SecureStore.setItemAsync(AVAILABILITY_STORAGE_KEY, JSON.stringify(data));
      }

      setAvailabilityData(data);
    } catch (error) {
      console.error('Error loading availability:', error);

      // Try to load from cache if available
      try {
        const cachedData = await SecureStore.getItemAsync(AVAILABILITY_STORAGE_KEY);
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          setAvailabilityData(parsedData);
          console.log('Loaded availability from cache');

          // Show offline indicator for network errors
          if (error instanceof Error && error.message.includes('Network')) {
            setIsOffline(true);
          } else if (showFullLoading) {
            Alert.alert('Error', 'Failed to load availability data. Using cached data.');
          }
        } else if (showFullLoading) {
          Alert.alert('Error', 'Failed to load availability data. Please try again.');
        }
      } catch (cacheError) {
        console.error('Error loading cached availability:', cacheError);
        if (showFullLoading) {
          Alert.alert('Error', 'Failed to load availability data. Please try again.');
        }
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Update availability
  const updateAvailability = async (availabilityId: string, data: Partial<AvailabilityData>): Promise<boolean> => {
    try {
      // Update availability locally (demo mode)
      const updatedAvailability = { ...data };

      // Update local state
      setAvailabilityData(prevData =>
        prevData.map(item => item.id === availabilityId ? { ...item, ...updatedAvailability } : item)
      );
      
      // Update cache
      await SecureStore.setItemAsync(AVAILABILITY_STORAGE_KEY, JSON.stringify(availabilityData));
      
      return true;
    } catch (error) {
      console.error('Error updating availability:', error);
      Alert.alert('Error', 'Failed to update availability. Please try again.');
      return false;
    }
  };

  // Create availability
  const createAvailability = async (data: Omit<AvailabilityData, 'id'>): Promise<boolean> => {
    try {
      // Create availability locally (demo mode)
      const newAvailability = { ...data, id: Date.now().toString() };

      // Update local state
      setAvailabilityData(prevData => [...prevData, newAvailability]);
      
      // Update cache
      await SecureStore.setItemAsync(AVAILABILITY_STORAGE_KEY, JSON.stringify([...availabilityData, newAvailability]));
      
      return true;
    } catch (error) {
      console.error('Error creating availability:', error);
      Alert.alert('Error', 'Failed to create availability. Please try again.');
      return false;
    }
  };

  // Delete availability
  const deleteAvailability = async (availabilityId: string): Promise<boolean> => {
    try {
      await availabilityService.deleteAvailability(availabilityId);
      
      // Update local state
      setAvailabilityData(prevData => prevData.filter(item => item.id !== availabilityId));
      
      // Update cache
      await SecureStore.setItemAsync(
        AVAILABILITY_STORAGE_KEY, 
        JSON.stringify(availabilityData.filter(item => item.id !== availabilityId))
      );
      
      return true;
    } catch (error) {
      console.error('Error deleting availability:', error);
      Alert.alert('Error', 'Failed to delete availability. Please try again.');
      return false;
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    if (isAuthenticated && user) {
      setIsRefreshing(true);
      loadAvailability(false);
    }
  };

  // Load availability when authenticated
  useEffect(() => {
    if (isAuthenticated && user && user.role === 'PROVIDER') {
      loadAvailability();
    }
  }, [isAuthenticated, user]);

  return (
    <ProviderAvailabilityContext.Provider
      value={{
        availabilityData,
        isLoading,
        isRefreshing,
        isOffline,
        loadAvailability,
        updateAvailability,
        createAvailability,
        deleteAvailability,
        handleRefresh,
      }}
    >
      {children}
    </ProviderAvailabilityContext.Provider>
  );
};

// Custom hook to use the provider availability context
export const useProviderAvailability = () => useContext(ProviderAvailabilityContext);
