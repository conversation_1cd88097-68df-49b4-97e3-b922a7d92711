import React, { createContext, useState, useEffect, useContext } from 'react';
import authService, { logout as logoutService } from '../services/authService';
import { User, UserRole } from '../types/user';
import { API_BASE_URL } from '../config/constants';
import * as SecureStore from 'expo-secure-store';
import * as authStorage from '../utils/authStorage';

// Auth context types
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  userRole: UserRole | null; // Added explicit userRole state
  login: (loginData: any) => Promise<any>;
  register: (firstName: string, lastName: string, email: string, password: string, role: string, phone?: string) => Promise<any>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<any>;
  resetPassword: (email: string, otp: string, newPassword: string) => Promise<any>;
  verifyEmail: (email: string, otp: string) => Promise<any>;
  verifyOtp: (verifyData: { email?: string; phone?: string; otp: string; purpose?: string }) => Promise<any>;
  error: string | null;
  clearError: () => void;
  setUser: (user: User | null) => void;
  setIsAuthenticated: (value: boolean) => void;
  setUserRole: (role: UserRole) => Promise<void>;
  getUserRole: () => Promise<UserRole | null>; // Added method to get current role
  refreshToken: () => Promise<boolean>;
  showAuthModal: () => void; // Added method to show auth modal
  hideAuthModal: () => void; // Added method to hide auth modal
  isAuthModalVisible: boolean; // Added state for auth modal visibility
}

// Create context
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  userRole: null,
  login: async () => ({}),
  register: async () => ({}),
  logout: async () => {},
  forgotPassword: async () => ({}),
  resetPassword: async () => ({}),
  verifyEmail: async () => ({}),
  verifyOtp: async () => ({}),
  error: null,
  clearError: () => {},
  setUser: () => {},
  setIsAuthenticated: () => {},
  setUserRole: async () => {},
  getUserRole: async () => null,
  refreshToken: async () => false,
  showAuthModal: () => {},
  hideAuthModal: () => {},
  isAuthModalVisible: false,
});

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userRole, setUserRoleState] = useState<UserRole | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastTokenRefresh, setLastTokenRefresh] = useState<number>(0);
  const [isAuthModalVisible, setIsAuthModalVisible] = useState<boolean>(false);

  // Clear error
  const clearError = () => setError(null);

  // Token refresh function
  const refreshToken = async (): Promise<boolean> => {
    try {
      console.log('Attempting to refresh token...');

      // Don't refresh too frequently (limit to once every 1 minute)
      const now = Date.now();
      if (now - lastTokenRefresh < 60 * 1000) {
        console.log('Token was refreshed recently, skipping');
        return true;
      }

      // Get the refresh token from storage
      const refreshTokenValue = await authStorage.getRefreshToken();

      if (!refreshTokenValue) {
        console.log('No refresh token found in storage');
        return false;
      }

      console.log('Found refresh token, attempting to refresh...');

      // Call the refresh token endpoint
      try {
        const response = await fetch(`${API_BASE_URL}/api/auth/refresh-token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ refreshToken: refreshTokenValue }),
        });

        if (!response.ok) {
          throw new Error(`Refresh token failed with status ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.data) {
          console.log('Successfully refreshed token');

          // Store the new tokens
          await authStorage.saveAuthTokens(
            data.data.accessToken,
            data.data.refreshToken
          );

          setLastTokenRefresh(now);
          return true;
        } else {
          throw new Error('Invalid response from refresh token endpoint');
        }
      } catch (error) {
        console.error('Error refreshing token:', error);
        return false;
      }
    } catch (error) {
      console.error('Error in refresh token flow:', error);
      return false;
    }
  };

  // Load user data on mount
  useEffect(() => {
    const loadUser = async () => {
      try {
        setIsLoading(true);
        console.log('Loading user data on app start...');

        // Check if user is authenticated
        const isAuthenticatedState = await authStorage.getAuthState();

        if (isAuthenticatedState) {
          console.log('User is authenticated according to stored state');

          // Load user data
          const userData = await authStorage.getUserData();

          if (userData) {
            console.log('Found cached user data:', userData.email || userData.phone);
            setUser(userData);
            setIsAuthenticated(true);

            // Load user role
            const role = await authStorage.getUserRole();
            if (role) {
              console.log('Loaded user role on app start:', role);
              setUserRoleState(role);
            } else if (userData.role) {
              // If role not in storage but in user data, use that
              setUserRoleState(userData.role);
              // And save it to storage for future use
              await authStorage.saveUserRole(userData.role);
            }

            // Try to refresh the token in the background
            refreshToken()
              .then(isValid => {
                if (!isValid) {
                  console.log('Token refresh failed');
                  // If refresh fails, we'll still keep the user logged in
                  // but will try to refresh again on the next API call
                }
              })
              .catch(error => {
                console.error('Error refreshing token:', error);
              });
          } else {
            console.log('No user data found despite being authenticated');

            // Check if we have an access token
            const accessToken = await authStorage.getAccessToken();

            if (accessToken) {
              // We have a token but no user data, try to refresh
              const refreshSuccess = await refreshToken();

              if (!refreshSuccess) {
                // If refresh fails, clear auth state
                setUser(null);
                setIsAuthenticated(false);
                await authStorage.clearAuthData();
              }
            } else {
              // No token and no user data, clear auth state
              setUser(null);
              setIsAuthenticated(false);
              await authStorage.clearAuthData();
            }
          }
        } else {
          // Not authenticated, but still load role preference if available
          const role = await authStorage.getUserRole();
          if (role) {
            console.log('Loaded user role preference (not authenticated):', role);
            setUserRoleState(role);
          }

          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error loading user:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // Login function
  const login = async (loginData: any) => {
    try {
      setIsLoading(true);
      clearError();

      // Call login API
      const response = await authService.login(loginData);

      // Check if this is an OTP response (for customer phone login)
      if (response.isOtpRequired) {
        // Return the OTP response for the UI to handle
        return response;
      }

      // Set user data from the response
      const userData = response.user;
      if (userData) {
        // Update state
        setUser(userData);
        setIsAuthenticated(true);

        // Save authentication state
        await authStorage.saveAuthState(true);

        // Save user data
        await authStorage.saveUserData(userData);

        // Save tokens if available
        if (response.token) {
          await authStorage.saveAuthTokens(response.token);
        } else if (response.tokens) {
          await authStorage.saveAuthTokens(
            response.tokens.accessToken,
            response.tokens.refreshToken
          );
        }

        // Update and save user role
        if (userData.role) {
          setUserRoleState(userData.role);
          await authStorage.saveUserRole(userData.role);
          console.log('Updated user role from login response:', userData.role);
        } else {
          // If no role in response, try to get from storage
          const storedRole = await authStorage.getUserRole();
          if (storedRole) {
            // Update the user object with the stored role
            const updatedUser = { ...userData, role: storedRole };
            setUser(updatedUser);
            await authStorage.saveUserData(updatedUser);
            console.log('Applied stored role to user:', storedRole);
          }
        }
      }

      return response;
    } catch (error: any) {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (firstName: string, lastName: string, email: string, password: string, role: string, phone?: string) => {
    try {
      setIsLoading(true);
      clearError();

      // Get current role from state or storage
      const currentRole = await getUserRole();

      // If role is not provided but we have a stored role, use that
      if (!role && currentRole) {
        role = currentRole.toString();
        console.log('Using stored role for registration:', role);
      }

      // Call register API with appropriate data
      const userData: any = {
        firstName,
        lastName,
        role,
      };

      // Add email and password for providers
      if (role === 'PROVIDER') {
        userData.email = email;
        userData.password = password;
        if (phone) userData.phone = phone;
      }
      // Add phone for customers
      else if (role === 'CUSTOMER') {
        userData.phone = phone;
        // Email is optional for customers
        if (email) userData.email = email;
      }

      // Call register API
      const response = await authService.register(userData);

      // If registration is successful, store the role
      if (response && response.success) {
        // Convert string role to enum if needed
        let userRoleEnum: UserRole;
        if (typeof role === 'string') {
          userRoleEnum = role.toLowerCase() === 'provider' ? UserRole.PROVIDER : UserRole.CUSTOMER;
        } else {
          userRoleEnum = role as unknown as UserRole;
        }

        // Store the role
        await setUserRole(userRoleEnum);
        console.log('Stored role after successful registration:', userRoleEnum);
      }

      // Return the response - we don't set the user as authenticated yet
      // because they need to verify their email/phone first
      return response;
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message || 'Registration failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Verify email function (legacy)
  const verifyEmail = async (email: string, otp: string) => {
    try {
      setIsLoading(true);
      clearError();

      // Call verify email API
      const response = await authService.verifyEmail(email, otp);

      // After successful verification, user should login
      return response;
    } catch (error: any) {
      console.error('Email verification error:', error);
      setError(error.message || 'Email verification failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP function (for both email and phone)
  const verifyOtp = async (verifyData: { email?: string; phone?: string; otp: string; purpose?: string }) => {
    try {
      setIsLoading(true);
      clearError();

      // Call verify OTP API
      const response = await authService.verifyOtp(verifyData);

      // If verification includes user data and tokens, update auth state
      if (response.user && response.tokens) {
        // Update state
        setUser(response.user);
        setIsAuthenticated(true);

        // Save authentication state
        await authStorage.saveAuthState(true);

        // Save user data
        await authStorage.saveUserData(response.user);

        // Save tokens
        await authStorage.saveAuthTokens(
          response.tokens.accessToken,
          response.tokens.refreshToken
        );

        // Update and save user role
        if (response.user.role) {
          setUserRoleState(response.user.role);
          await authStorage.saveUserRole(response.user.role);
        }
      }

      return response;
    } catch (error: any) {
      console.error('OTP verification error:', error);
      setError(error.message || 'Verification failed. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Set user role function
  const setUserRole = async (role: UserRole) => {
    try {
      console.log('Setting user role to:', role);

      // Store the role in storage
      await authStorage.saveUserRole(role);

      // Update the role in state
      setUserRoleState(role);

      // If a user is logged in, update their role
      if (user) {
        // Update the user object with the new role
        const updatedUser = { ...user, role };

        // Update the user in state
        setUser(updatedUser);

        // Update the user in storage
        await authStorage.saveUserData(updatedUser);

        console.log('User role updated successfully to:', role);
      } else {
        console.log('Role preference saved, but no user is logged in yet');
      }
    } catch (error) {
      console.error('Error setting user role:', error);
    }
  };

  // Get user role function
  const getUserRole = async (): Promise<UserRole | null> => {
    try {
      // First check state
      if (userRole) {
        return userRole;
      }

      // Then check user object
      if (user?.role) {
        return user.role;
      }

      // Then check storage
      const storedRole = await authStorage.getUserRole();
      if (storedRole) {
        setUserRoleState(storedRole);
        return storedRole;
      }

      return null;
    } catch (error) {
      console.error('Error getting user role:', error);
      return null;
    }
  };

  // Auth modal functions
  const showAuthModal = () => {
    setIsAuthModalVisible(true);
  };

  const hideAuthModal = () => {
    setIsAuthModalVisible(false);
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);

      // Save the current role before logout to preserve it
      const currentRole = userRole;

      // Clear user data and credentials from state
      setUser(null);
      setIsAuthenticated(false);

      // Clear all authentication data from storage
      await authStorage.clearAuthData();

      // Restore the user role preference after logout
      if (currentRole) {
        await authStorage.saveUserRole(currentRole);
        setUserRoleState(currentRole);
        console.log('Preserved user role after logout:', currentRole);
      }

      // For backward compatibility, also call the legacy logout service
      await logoutService();

    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Forgot password function
  const forgotPassword = async (email: string) => {
    try {
      setIsLoading(true);
      clearError();

      // Call forgot password API
      const response = await authService.forgotPassword(email);
      return response;
    } catch (error: any) {
      console.error('Forgot password error:', error);
      setError(error.message || 'Failed to send password reset email. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (email: string, otp: string, newPassword: string) => {
    try {
      setIsLoading(true);
      clearError();

      // Call reset password API
      const response = await authService.resetPassword(email, otp, newPassword);
      return response;
    } catch (error: any) {
      console.error('Reset password error:', error);
      setError(error.message || 'Failed to reset password. Please try again.');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated,
        userRole,
        login,
        register,
        logout,
        forgotPassword,
        resetPassword,
        verifyEmail,
        verifyOtp,
        error,
        clearError,
        setUser,
        setIsAuthenticated,
        setUserRole,
        getUserRole,
        refreshToken,
        showAuthModal,
        hideAuthModal,
        isAuthModalVisible,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => useContext(AuthContext);
