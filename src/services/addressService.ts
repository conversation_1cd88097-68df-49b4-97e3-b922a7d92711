import type { Location } from '../context/LocationContext';
import { mockAddresses } from '../data/mockAddresses';
import api from '../api/config';

// Interface for address data sent to/from the API
export interface AddressData {
  id?: string;
  label: string;
  address: string;
  area?: string;
  isPrimary: boolean;
  latitude?: number;
  longitude?: number;
  plusCode?: string;
}

// API response interface
interface ApiResponse {
  success: boolean;
  data: any;
  message?: string;
}

// Convert Location from context to AddressData for API
const locationToAddressData = (location: Location): AddressData => {
  return {
    id: location.id.startsWith('loc_') ? undefined : location.id, // Don't send temporary IDs
    label: location.label,
    address: location.address,
    area: location.area,
    isPrimary: location.isPrimary || false,
    latitude: location.coordinates?.latitude,
    longitude: location.coordinates?.longitude,
    plusCode: location.plusCode,
  };
};

// Convert AddressData from API to Location for context
const addressDataToLocation = (data: any): Location => {
  return {
    id: data.id,
    label: data.label,
    address: data.address,
    area: data.area,
    isPrimary: data.isPrimary,
    coordinates: data.latitude && data.longitude ? {
      latitude: data.latitude,
      longitude: data.longitude
    } : undefined,
    plusCode: data.plusCode,
  };
};

// Get all addresses for the current user - connects to real backend API
export const getUserAddresses = async (): Promise<Location[]> => {
  try {
    console.log('Fetching addresses from API');

    // Call the real API endpoint
    const response = await api.get<ApiResponse>('/api/addresses');

    if (response.data.success) {
      console.log('Successfully fetched addresses from API');

      // Convert API response to Location objects
      const addresses = Array.isArray(response.data.data)
        ? response.data.data.map(addressDataToLocation)
        : [];

      return addresses;
    } else {
      throw new Error(response.data.message || 'Failed to get addresses');
    }
  } catch (error) {
    console.error('Error fetching addresses from API, falling back to mock data:', error);
    console.warn('Using mock addresses data as fallback');

    // Fallback to mock data if API call fails
    return mockAddresses.map(addr => ({
      id: addr.id,
      label: addr.label,
      address: addr.address,
      area: addr.area || '',
      isPrimary: addr.isPrimary,
      coordinates: addr.latitude && addr.longitude ? {
        latitude: addr.latitude,
        longitude: addr.longitude
      } : undefined,
    }));
  }
};

// Create a new address - connects to real backend API
export const createAddress = async (location: Location): Promise<Location> => {
  try {
    console.log('Creating address with API:', location);

    // Convert Location to AddressData for API
    const addressData = locationToAddressData(location);

    // Call the real API endpoint
    const response = await api.post<ApiResponse>('/api/addresses', addressData);

    if (response.data.success) {
      console.log('Successfully created address with API');
      return addressDataToLocation(response.data.data);
    } else {
      throw new Error(response.data.message || 'Failed to create address');
    }
  } catch (error) {
    console.error('Error creating address with API, falling back to mock implementation:', error);
    console.warn('Using mock address creation as fallback');

    // Fallback to mock implementation if API call fails
    // Create a new location with a mock ID
    const newLocation: Location = {
      ...location,
      id: location.id.startsWith('loc_') ? `addr-${Date.now()}` : location.id
    };

    return newLocation;
  }
};

// Update an existing address - connects to real backend API
export const updateAddress = async (location: Location): Promise<Location> => {
  try {
    console.log('Updating address with API:', location);

    // Convert Location to AddressData for API
    const addressData = locationToAddressData(location);

    // Call the real API endpoint
    const response = await api.put<ApiResponse>(`/api/addresses/${location.id}`, addressData);

    if (response.data.success) {
      console.log('Successfully updated address with API');
      return addressDataToLocation(response.data.data);
    } else {
      throw new Error(response.data.message || 'Failed to update address');
    }
  } catch (error) {
    console.error('Error updating address with API, falling back to mock implementation:', error);
    console.warn('Using mock address update as fallback');

    // Fallback to mock implementation if API call fails
    return location;
  }
};

// Delete an address - connects to real backend API
export const deleteAddress = async (addressId: string): Promise<void> => {
  try {
    console.log('Deleting address with API:', addressId);

    // Call the real API endpoint
    const response = await api.delete<ApiResponse>(`/api/addresses/${addressId}`);

    if (response.data.success) {
      console.log('Successfully deleted address with API');
      return;
    } else {
      throw new Error(response.data.message || 'Failed to delete address');
    }
  } catch (error) {
    console.error('Error deleting address with API, falling back to mock implementation:', error);
    console.warn('Using mock address deletion as fallback');

    // Fallback to mock implementation if API call fails
    return;
  }
};
