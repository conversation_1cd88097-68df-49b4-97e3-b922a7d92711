import axios from 'axios';
import { API_BASE_URL } from '../config/constants';
import * as authStorage from '../utils/authStorage';
import { UserRole } from '../types/user';

// Define types for authentication responses
interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string;
    email?: string;
    phone?: string;
    firstName: string;
    lastName: string;
    role: string;
  };
}

interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}

interface OtpResponse {
  isNewUser?: boolean;
  phone?: string;
  email?: string;
  message: string;
  otp?: string; // Only in development
}

// Create axios instance for auth requests
const authApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add interceptor to handle token expiration
authApi.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 and we haven't tried to refresh the token yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Get refresh token from storage
        const refreshToken = await authStorage.getRefreshToken();

        if (!refreshToken) {
          // No refresh token, user needs to login again
          await logout();
          return Promise.reject(error);
        }

        // Try to get a new access token
        const response = await axios.post(`${API_BASE_URL}/api/auth/refresh-token`, {
          refreshToken,
        });

        const { accessToken, refreshToken: newRefreshToken } = response.data;

        // Store new tokens using the auth storage service
        await authStorage.saveAuthTokens(accessToken, newRefreshToken);

        // Update auth header and retry the original request
        originalRequest.headers['Authorization'] = `Bearer ${accessToken}`;
        return authApi(originalRequest);
      } catch (refreshError) {
        // If refresh fails, logout the user
        await logout();
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Add auth token to requests if available
authApi.interceptors.request.use(
  async (config) => {
    const token = await authStorage.getAccessToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Authentication service functions
export const authService = {
  // Register a new user
  async register(firstName: string, lastName: string, email: string, password: string, role: string, phone?: string) {
    const userData: any = {
      firstName,
      lastName,
      role,
    };

    // Add email and password for providers
    if (role === 'PROVIDER') {
      userData.email = email;
      userData.password = password;
      if (phone) userData.phone = phone;
    }
    // Add phone for customers
    else if (role === 'CUSTOMER') {
      userData.phone = phone;
      // Email is optional for customers
      if (email) userData.email = email;
    }

    const response = await authApi.post<{ message: string; userId: string }>('/api/auth/register', userData);
    return response.data;
  },

  // Verify OTP (email or phone)
  async verifyOtp(verifyData: { email?: string; phone?: string; otp: string; purpose?: string }) {
    const response = await authApi.post<{ message: string; user?: any; tokens?: any }>('/api/auth/verify-otp', verifyData);

    // If verification includes tokens and user data, store them
    if (response.data.tokens && response.data.user) {
      const { tokens, user } = response.data;
      await authStorage.saveAuthTokens(tokens.accessToken, tokens.refreshToken);
      await authStorage.saveUserData(user);
      await authStorage.saveAuthState(true);

      // Store user role if available
      if (user.role) {
        await authStorage.saveUserRole(user.role);
      }
    }

    return response.data;
  },

  // Legacy verify email with OTP
  async verifyEmail(email: string, otp: string) {
    return await this.verifyOtp({ email, otp, purpose: 'EMAIL_VERIFICATION' });
  },

  // Login user
  async login(loginData: { email?: string; password?: string; phone?: string; role: string }) {
    const response = await authApi.post<AuthResponse | OtpResponse>('/api/auth/login', loginData);

    // Check if this is a provider login with tokens
    if ('accessToken' in response.data && 'refreshToken' in response.data && 'user' in response.data) {
      const { accessToken, refreshToken, user } = response.data;

      // Store tokens and user data
      await authStorage.saveAuthTokens(accessToken, refreshToken);
      await authStorage.saveUserData(user);
      await authStorage.saveAuthState(true);

      // Store user role
      if (user.role) {
        await authStorage.saveUserRole(user.role);
      }

      return {
        success: true,
        user,
        tokens: { accessToken, refreshToken }
      };
    }
    // Customer login with OTP
    else if ('message' in response.data) {
      return {
        success: true,
        isOtpRequired: true,
        phone: response.data.phone,
        isNewUser: response.data.isNewUser,
        message: response.data.message,
        otp: response.data.otp // Only in development
      };
    }

    throw new Error('Invalid response format from login endpoint');
  },

  // Refresh access token
  async refreshToken(refreshToken: string) {
    const response = await authApi.post<TokenResponse>('/api/auth/refresh-token', {
      refreshToken,
    });

    const { accessToken, refreshToken: newRefreshToken } = response.data;

    // Store new tokens using the auth storage service
    await authStorage.saveAuthTokens(accessToken, newRefreshToken);

    return response.data;
  },

  // Request password reset
  async forgotPassword(email: string) {
    const response = await authApi.post<{ message: string }>('/api/auth/forgot-password', {
      email,
    });
    return response.data;
  },

  // Reset password with OTP
  async resetPassword(email: string, otp: string, newPassword: string) {
    const response = await authApi.post<{ message: string }>('/api/auth/reset-password', {
      email,
      otp,
      newPassword,
    });
    return response.data;
  },

  // Get current user data
  async getCurrentUser() {
    return await authStorage.getUserData();
  },

  // Check if user is authenticated
  async isAuthenticated() {
    return await authStorage.getAuthState();
  },
};

// Logout function
export const logout = async () => {
  // Clear all auth-related data using the auth storage service
  await authStorage.clearAuthData();

  console.log('Logout completed: All auth data cleared');
};

export default authService;
