import { Service, ServiceWithProviders } from '../../types/service';
import api, { API_URL } from '../config';

// Types
export interface ServiceResponse {
  success: boolean;
  data: Service | Service[] | ServiceWithProviders;
  message?: string;
}

// Service service methods - using real backend API with fallback to mock data
const serviceService = {
  /**
   * Get all services - connects to real backend API
   */
  getServices: async (): Promise<Service[]> => {
    try {
      console.log('Fetching services from API');
      console.log('Using API URL:', API_URL);

      // Sample services data for when the API is not available
      const sampleServices: Service[] = [
        {
          id: '1',
          name: 'Regular Home Cleaning',
          title: 'Regular Home Cleaning',
          description: 'Standard cleaning service for homes',
          image: 'https://picsum.photos/id/26/600/300',
          price: 100,
          basePrice: 100,
          duration: '2 hours',
          isActive: true,
          features: ['Dusting', 'Vacuuming', 'Mopping', 'Bathroom cleaning', 'Kitchen cleaning'],
          rating: 4.8,
          reviewCount: 120
        },
        {
          id: '2',
          name: 'Deep Cleaning',
          title: 'Deep Cleaning',
          description: 'Thorough cleaning of all areas',
          image: 'https://picsum.photos/id/28/600/300',
          price: 200,
          basePrice: 200,
          duration: '4 hours',
          isActive: true,
          features: ['All regular cleaning tasks', 'Inside cabinets', 'Inside refrigerator', 'Inside oven', 'Window cleaning'],
          rating: 4.9,
          reviewCount: 85
        },
        {
          id: '3',
          name: 'Office Cleaning',
          title: 'Office Cleaning',
          description: 'Professional cleaning for offices',
          image: 'https://picsum.photos/id/30/600/300',
          price: 150,
          basePrice: 150,
          duration: '3 hours',
          isActive: true,
          features: ['Desk cleaning', 'Floor cleaning', 'Trash removal', 'Bathroom sanitization', 'Kitchen area cleaning'],
          rating: 4.7,
          reviewCount: 65
        }
      ];

      try {
        // Call the real API endpoint
        const response = await api.get<ServiceResponse>('/api/services');

        if (response.data.success) {
          console.log('Successfully fetched services from API');
          return response.data.data as Service[];
        } else {
          console.warn('API returned error, using sample data');
          return sampleServices;
        }
      } catch (error: any) {
        console.error('Error getting services from API, using sample data:', error);
        console.log('Using sample services data as fallback');
        return sampleServices;
      }
    } catch (error: any) {
      console.error('Unexpected error in getServices:', error);
      throw new Error('Failed to fetch services. Please try again later.');
    }
  },

  /**
   * Get service by ID - connects to real backend API
   */
  getService: async (id: string): Promise<ServiceWithProviders> => {
    try {
      console.log('Fetching service by ID from API:', id);
      console.log('Using API URL:', API_URL);

      // For testing purposes, create a sample service if we can't connect to the API
      // This is a temporary solution until the backend connection is fixed
      const sampleService: ServiceWithProviders = {
        id: id,
        name: 'Sample Service',
        title: 'Sample Service',
        description: 'This is a sample service created when the API connection fails.',
        longDescription: 'This is a sample service created when the API connection fails. The backend server might not be running or there might be network connectivity issues.',
        price: 1500,
        basePrice: 1500,
        image: 'https://picsum.photos/id/26/600/300',
        isActive: true,
        duration: '2 hours',
        features: ['Feature 1', 'Feature 2', 'Feature 3'],
        rating: 4.5,
        reviewCount: 10,
        reviews: [
          {
            id: '1',
            user: 'Sample User',
            rating: 5,
            comment: 'Great service!',
            date: '2023-05-01',
            avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
          }
        ],
        providers: []
      };

      try {
        // Call the real API endpoint with increased timeout
        const response = await api.get<ServiceResponse>(`/api/services/${id}`, {
          timeout: 30000 // Increase timeout to 30 seconds
        });

        if (response.data.success) {
          console.log('Successfully fetched service from API');

          // Get the service data from the response
          const serviceData = response.data.data;

          // Normalize the data to match our frontend Service interface
          const normalizedService: ServiceWithProviders = {
            id: serviceData.id,
            name: serviceData.name || '',
            title: serviceData.name || '', // Use name as title for UI consistency
            description: serviceData.description || '',
            longDescription: serviceData.description || '', // Use description as longDescription if not provided
            price: serviceData.price || 0,
            basePrice: serviceData.price || 0,
            image: serviceData.image || 'https://picsum.photos/id/26/600/300', // Default image if not provided
            isActive: serviceData.isActive !== undefined ? serviceData.isActive : true,
            // Add default values for UI-specific fields
            duration: serviceData.duration || '2 hours', // Default duration
            features: serviceData.features || [], // Empty features array by default
            rating: serviceData.rating || 0, // Default rating
            reviewCount: serviceData.reviewCount || 0, // Default review count
            reviews: serviceData.reviews || [], // Empty reviews array by default
            providers: serviceData.providers || [] // Empty providers array by default
          };

          return normalizedService;
        } else {
          console.warn('API returned error, using sample data');
          return sampleService;
        }
      } catch (error: any) {
        console.error('Error getting service from API, using sample data:', error);
        console.log('Using sample service data as fallback');
        return sampleService;
      }
    } catch (error: any) {
      console.error('Unexpected error in getService:', error);
      throw new Error('Failed to fetch service details. Please try again later.');
    }
  },

  /**
   * Get available providers for a service
   */
  getServiceProviders: async (serviceId: string, date: string, time: string): Promise<any[]> => {
    try {
      console.log('Fetching available providers for service from API:', serviceId);
      console.log('Using API URL:', API_URL);

      // Sample providers data for when the API is not available
      const sampleProviders = [
        {
          id: 'prov1',
          name: 'Mariama Jallow',
          image: 'https://randomuser.me/api/portraits/women/44.jpg',
          phone: '+220 7123456',
          rating: 4.9,
          jobs: 120,
          verified: true,
          price: 150
        },
        {
          id: 'prov2',
          name: 'Ousman Ceesay',
          image: 'https://randomuser.me/api/portraits/men/32.jpg',
          phone: '+220 7654321',
          rating: 4.7,
          jobs: 85,
          verified: true,
          price: 140
        },
        {
          id: 'prov3',
          name: 'Fatou Saine',
          image: 'https://randomuser.me/api/portraits/women/68.jpg',
          phone: '+220 7987654',
          rating: 4.8,
          jobs: 95,
          verified: true,
          price: 160
        }
      ];

      try {
        // Call the real API endpoint
        const response = await api.get<any>(`/api/providers/available`, {
          params: {
            serviceId,
            date,
            time
          },
          timeout: 30000 // Increase timeout to 30 seconds
        });

        if (response.data.success) {
          console.log('Successfully fetched available providers from API');
          return response.data.data;
        } else {
          console.warn('API returned error, using sample data');
          return sampleProviders;
        }
      } catch (error: any) {
        console.error('Error getting available providers from API, using sample data:', error);
        console.log('Using sample providers data as fallback');
        return sampleProviders;
      }
    } catch (error: any) {
      console.error('Unexpected error in getServiceProviders:', error);
      throw new Error('Failed to fetch available providers. Please try again later.');
    }
  }
};

export default serviceService;
