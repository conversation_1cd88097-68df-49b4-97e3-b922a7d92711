import api from '../config';
import { Service } from '../../types/service';

// Types
export interface ProviderServiceResponse {
  success: boolean;
  data: ProviderServiceData | ProviderServiceData[];
  message?: string;
}

export interface ProviderServiceData {
  id: string;
  providerId: string;
  serviceId: string;
  price?: number;
  service: Service;
}

export interface AddProviderServiceData {
  serviceId: string;
  price?: number;
}

export interface AvailabilityResponse {
  success: boolean;
  data: AvailabilityData | AvailabilityData[];
  message?: string;
}

export interface AvailabilityData {
  id: string;
  providerId: string;
  dayOfWeek: number; // 0-6 for Sunday-Saturday
  startTime: string; // Format: "HH:MM" in 24-hour format
  endTime: string; // Format: "HH:MM" in 24-hour format
  isRecurring: boolean;
  specificDate?: string;
}

export interface AddAvailabilityData {
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isRecurring: boolean;
  specificDate?: string;
}

// Provider service methods
const providerService = {
  /**
   * Get provider services
   */
  getProviderServices: async (): Promise<ProviderServiceData[]> => {
    try {
      const response = await api.get<ProviderServiceResponse>('/api/provider/services');

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to get provider services');
      }

      return response.data.data as ProviderServiceData[];
    } catch (error: any) {
      throw error.response?.data || { success: false, error: 'Failed to get provider services' };
    }
  },

  /**
   * Add service to provider
   */
  addProviderService: async (data: AddProviderServiceData): Promise<ProviderServiceData> => {
    try {
      const response = await api.post<ProviderServiceResponse>('/api/provider/services', data);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to add provider service');
      }

      return response.data.data as ProviderServiceData;
    } catch (error: any) {
      throw error.response?.data || { success: false, error: 'Failed to add provider service' };
    }
  },

  /**
   * Remove service from provider
   */
  removeProviderService: async (serviceId: string): Promise<{ serviceId: string }> => {
    try {
      const response = await api.delete<ProviderServiceResponse>(`/api/provider/services?serviceId=${serviceId}`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to remove provider service');
      }

      return { serviceId };
    } catch (error: any) {
      throw error.response?.data || { success: false, error: 'Failed to remove provider service' };
    }
  },

  /**
   * Get provider availability
   */
  getProviderAvailability: async (): Promise<AvailabilityData[]> => {
    try {
      const response = await api.get<AvailabilityResponse>('/api/provider/availability');

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to get provider availability');
      }

      return response.data.data as AvailabilityData[];
    } catch (error: any) {
      throw error.response?.data || { success: false, error: 'Failed to get provider availability' };
    }
  },

  /**
   * Add provider availability
   */
  addProviderAvailability: async (data: AddAvailabilityData): Promise<AvailabilityData> => {
    try {
      const response = await api.post<AvailabilityResponse>('/api/provider/availability', data);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to add provider availability');
      }

      return response.data.data as AvailabilityData;
    } catch (error: any) {
      throw error.response?.data || { success: false, error: 'Failed to add provider availability' };
    }
  },

  /**
   * Remove provider availability
   */
  removeProviderAvailability: async (id: string): Promise<{ id: string }> => {
    try {
      const response = await api.delete<AvailabilityResponse>(`/api/provider/availability?id=${id}`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to remove provider availability');
      }

      return { id };
    } catch (error: any) {
      throw error.response?.data || { success: false, error: 'Failed to remove provider availability' };
    }
  },
};

export default providerService;
