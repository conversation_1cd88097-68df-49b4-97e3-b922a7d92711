import { BookingStatus } from '../../types/booking';
import api from '../config';

// Helper function to format payment method for backend
const formatPaymentMethodForBackend = (method: string): string => {
  // Convert to uppercase and normalize
  const normalizedMethod = method.toUpperCase().replace(/\s/g, '_');

  // Map to backend expected values
  if (normalizedMethod === 'PAY_LATER' || normalizedMethod === 'PAY_ON_WORK_DONE') {
    return 'PAY_LATER';
  } else if (normalizedMethod === 'MOBILE_MONEY') {
    return 'MOBILE_MONEY_UPLOAD';
  }

  // Default fallback
  return 'PAY_LATER';
};

// Types
export interface PaymentResponse {
  success: boolean;
  data: any;
  message?: string;
}

export interface PaymentData {
  bookingId: string;
  paymentMethod: PaymentMethod;
  amount: number;
  receiptImage?: string; // Base64 encoded image for mobile money
  cardDetails?: CardDetails; // For credit card payments
  bankDetails?: BankDetails; // For bank transfer payments
}

export interface CardDetails {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
}

export interface BankDetails {
  accountNumber: string;
  bankName: string;
  accountName: string;
  reference: string;
}

export enum PaymentMethod {
  MOBILE_MONEY = 'MOBILE_MONEY',
  CREDIT_CARD = 'CREDIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  CASH = 'CASH',
  PAY_LATER = 'PAY_LATER'
}

export interface PaymentStatusData {
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
}

// Payment service methods - using real backend API with fallback to mock implementation
const paymentService = {
  /**
   * Process a payment for a booking - connects to real backend API
   */
  processPayment: async (data: PaymentData): Promise<any> => {
    try {
      console.log('Processing payment with API:', {
        ...data,
        receiptImage: data.receiptImage ? '[Image data]' : undefined
      });

      // Format payment method for backend
      const formattedData = {
        ...data,
        paymentMethod: formatPaymentMethodForBackend(data.paymentMethod)
      };

      // Add a short delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Call the real API endpoint
      const response = await api.post<PaymentResponse>('/api/payments', formattedData);

      if (response.data.success) {
        console.log('Successfully processed payment with API');
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to process payment');
      }
    } catch (error: any) {
      console.error('Error processing payment with API, falling back to mock implementation:', error);
      console.warn('Using mock payment processing as fallback');

      // Add a short delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Fallback to mock implementation if API call fails
      return {
        id: 'payment-' + Date.now(),
        bookingId: data.bookingId,
        amount: data.amount,
        method: data.paymentMethod,
        status: 'COMPLETED',
        timestamp: new Date().toISOString()
      };
    }
  },

  /**
   * Upload a receipt image for mobile money payment - connects to real backend API
   */
  uploadReceipt: async (bookingId: string, receiptImage: string): Promise<any> => {
    try {
      console.log('Uploading receipt with API for booking:', bookingId);

      // Call the real API endpoint
      const response = await api.post<PaymentResponse>(`/api/payments/${bookingId}/receipt`, {
        receiptImage
      });

      if (response.data.success) {
        console.log('Successfully uploaded receipt with API');
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to upload receipt');
      }
    } catch (error: any) {
      console.error('Error uploading receipt with API, falling back to mock implementation:', error);
      console.warn('Using mock receipt upload as fallback');

      // Fallback to mock implementation if API call fails
      return {
        uploaded: true,
        url: 'https://example.com/receipts/mock-receipt.jpg',
        bookingId
      };
    }
  },

  /**
   * Check payment status - connects to real backend API
   */
  checkPaymentStatus: async (paymentId: string): Promise<any> => {
    try {
      console.log('Checking payment status with API for:', paymentId);

      // Call the real API endpoint
      const response = await api.get<PaymentResponse>(`/api/payments/${paymentId}/status`);

      if (response.data.success) {
        console.log('Successfully checked payment status with API');
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to check payment status');
      }
    } catch (error: any) {
      console.error('Error checking payment status with API, falling back to mock implementation:', error);
      console.warn('Using mock payment status check as fallback');

      // Fallback to mock implementation if API call fails
      return {
        id: paymentId,
        status: 'COMPLETED',
        timestamp: new Date().toISOString()
      };
    }
  },

  /**
   * Process credit card payment - connects to real backend API
   */
  processCreditCardPayment: async (data: PaymentData): Promise<any> => {
    try {
      console.log('Processing credit card payment with API:', {
        ...data,
        cardDetails: data.cardDetails ? '****' : undefined
      });

      // Call the real API endpoint
      const response = await api.post<PaymentResponse>('/api/payments/credit-card', data);

      if (response.data.success) {
        console.log('Successfully processed credit card payment with API');
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to process credit card payment');
      }
    } catch (error: any) {
      console.error('Error processing credit card payment with API, falling back to mock implementation:', error);
      console.warn('Using mock credit card payment processing as fallback');

      // Fallback to mock implementation if API call fails
      return {
        id: 'payment-cc-' + Date.now(),
        bookingId: data.bookingId,
        amount: data.amount,
        method: PaymentMethod.CREDIT_CARD,
        status: 'COMPLETED',
        timestamp: new Date().toISOString(),
        cardLast4: data.cardDetails?.cardNumber.slice(-4) || '****'
      };
    }
  },

  /**
   * Process bank transfer payment - connects to real backend API
   */
  processBankTransferPayment: async (data: PaymentData): Promise<any> => {
    try {
      console.log('Processing bank transfer payment with API:', {
        ...data,
        bankDetails: data.bankDetails ? '****' : undefined
      });

      // Call the real API endpoint
      const response = await api.post<PaymentResponse>('/api/payments/bank-transfer', data);

      if (response.data.success) {
        console.log('Successfully processed bank transfer payment with API');
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to process bank transfer payment');
      }
    } catch (error: any) {
      console.error('Error processing bank transfer payment with API, falling back to mock implementation:', error);
      console.warn('Using mock bank transfer payment processing as fallback');

      // Fallback to mock implementation if API call fails
      return {
        id: 'payment-bt-' + Date.now(),
        bookingId: data.bookingId,
        amount: data.amount,
        method: PaymentMethod.BANK_TRANSFER,
        status: 'PENDING', // Bank transfers are typically pending until confirmed
        timestamp: new Date().toISOString(),
        reference: data.bankDetails?.reference || 'REF-' + Date.now()
      };
    }
  },

  /**
   * Get payment history - connects to real backend API
   */
  getPaymentHistory: async (): Promise<any[]> => {
    try {
      console.log('Fetching payment history with API');

      // Call the real API endpoint
      const response = await api.get<PaymentResponse>('/api/payments/history');

      if (response.data.success) {
        console.log('Successfully fetched payment history with API');
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch payment history');
      }
    } catch (error: any) {
      console.error('Error fetching payment history with API, falling back to mock implementation:', error);
      console.warn('Using mock payment history as fallback');

      // Fallback to mock implementation if API call fails
      return [
        {
          id: 'payment-1',
          bookingId: 'BK-12345',
          amount: 250,
          method: PaymentMethod.MOBILE_MONEY,
          status: 'COMPLETED',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'payment-2',
          bookingId: 'BK-12346',
          amount: 350,
          method: PaymentMethod.CREDIT_CARD,
          status: 'COMPLETED',
          timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          cardLast4: '4242'
        }
      ];
    }
  },
};

export default paymentService;
