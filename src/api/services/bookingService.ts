import { Booking, BookingStatus } from '../../types/booking';
import { mockBookings } from '../../data/mockBookings';
import api from '../config';

// Helper function to format date for backend (from "Tue, 16 May" to ISO format)
const formatDateForBackend = (date: string): string => {
  try {
    // Check if the date is already in ISO format
    if (date.match(/^\d{4}-\d{2}-\d{2}T/)) {
      return date; // Already in ISO format
    }

    // Handle different date formats
    if (date.includes(',')) {
      // Format: "Tue, 16 May"
      const parts = date.split(', ');
      if (parts.length !== 2) {
        throw new Error('Invalid date format');
      }

      const dayMonth = parts[1].split(' ');
      if (dayMonth.length !== 2) {
        throw new Error('Invalid date format');
      }

      const day = parseInt(dayMonth[0], 10);
      const month = dayMonth[1];

      // Map month name to month number
      const monthMap: { [key: string]: number } = {
        'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
        'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
      };

      if (!(month in monthMap)) {
        throw new Error('Invalid month');
      }

      // Create a date object with current year
      const currentYear = new Date().getFullYear();
      const dateObj = new Date(currentYear, monthMap[month], day);

      // Set time to noon to avoid timezone issues
      dateObj.setHours(12, 0, 0, 0);

      // Format as ISO string
      return dateObj.toISOString();
    } else if (date.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
      // Format: "MM/DD/YYYY"
      const [month, day, year] = date.split('/').map(Number);
      const dateObj = new Date(year, month - 1, day, 12, 0, 0, 0);
      return dateObj.toISOString();
    } else if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      // Format: "YYYY-MM-DD"
      const dateObj = new Date(`${date}T12:00:00Z`);
      return dateObj.toISOString();
    }

    // Try to parse as a date string
    const dateObj = new Date(date);
    if (!isNaN(dateObj.getTime())) {
      // Set time to noon to avoid timezone issues
      dateObj.setHours(12, 0, 0, 0);
      return dateObj.toISOString();
    }

    throw new Error('Unrecognized date format');
  } catch (error) {
    console.error('Error formatting date for backend:', error);
    // Fallback to current date at noon
    const now = new Date();
    now.setHours(12, 0, 0, 0);
    return now.toISOString();
  }
};

// Helper function to format time for backend (from "1:00 PM" to "13:00")
const formatTimeForBackend = (time: string): string => {
  try {
    // Check if already in 24-hour format (HH:MM)
    if (time.match(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
      // Already in correct format, ensure it has leading zeros
      const [hours, minutes] = time.split(':').map(Number);
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    // Parse 12-hour format with AM/PM (e.g., "1:00 PM")
    const timeRegex = /(\d+):(\d+)\s?(AM|PM)/i;
    const match = time.match(timeRegex);

    if (match) {
      let hours = parseInt(match[1], 10);
      const minutes = match[2];
      const period = match[3].toUpperCase();

      // Convert to 24-hour format
      if (period === 'PM' && hours < 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }

      // Format as HH:MM
      return `${hours.toString().padStart(2, '0')}:${minutes}`;
    }

    // Try to parse other formats
    if (time.includes(' ')) {
      // Might be something like "9 AM" or "10 PM"
      const [hourStr, period] = time.split(' ');
      const hour = parseInt(hourStr, 10);

      if (!isNaN(hour) && ['AM', 'PM'].includes(period.toUpperCase())) {
        let hours = hour;
        if (period.toUpperCase() === 'PM' && hours < 12) {
          hours += 12;
        } else if (period.toUpperCase() === 'AM' && hours === 12) {
          hours = 0;
        }

        return `${hours.toString().padStart(2, '0')}:00`;
      }
    }

    throw new Error('Invalid time format');
  } catch (error) {
    console.error('Error formatting time for backend:', error);
    // Fallback to current time
    const now = new Date();
    return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
  }
};

// Helper function to format payment method for backend
const formatPaymentMethodForBackend = (method: string): string => {
  // Convert to uppercase and normalize
  const normalizedMethod = method.toUpperCase().replace(/\s/g, '_');

  // Map to backend expected values
  if (normalizedMethod === 'PAY_LATER' || normalizedMethod === 'PAY_ON_WORK_DONE') {
    return 'PAY_LATER';
  } else if (normalizedMethod === 'MOBILE_MONEY' || normalizedMethod === 'MOBILE_MONEY_UPLOAD') {
    return 'MOBILE_MONEY_UPLOAD';
  }

  // Default fallback
  return 'PAY_LATER';
};

// Types
export interface BookingResponse {
  success: boolean;
  data: Booking | Booking[];
  message?: string;
}

export interface CreateBookingData {
  serviceId: string;
  addressId: string;
  date: string;
  startTime: string;
  endTime?: string; // Not used by backend
  notes?: string;
  providerId?: string;
  frequency?: 'ONE_TIME' | 'WEEKLY' | 'BI_WEEKLY' | 'MONTHLY';
  paymentMethod?: string;
  paymentId?: string;
  customerId?: string; // Not used by backend (comes from auth token)
  customerName?: string; // Not used by backend
  customerEmail?: string; // Not used by backend
  customerPhone?: string; // Not used by backend
  paymentProof?: string | null; // Handled separately via payment API
  price?: number; // Used to set totalAmount in backend
  total?: number; // Alternative to price
}

export interface UpdateBookingStatusData {
  status: BookingStatus;
}

export interface AutoAssignProviderData {
  bookingId: string;
}

// Booking service methods - using real backend API with fallback to mock data
const bookingService = {
  /**
   * Get all bookings - connects to real backend API
   */
  getBookings: async (status?: BookingStatus): Promise<Booking[]> => {
    try {
      console.log('Fetching bookings from API, status filter:', status);

      // Prepare query parameters
      const params: any = {};
      if (status) {
        params.status = status;
      }

      // Call the real API endpoint
      const response = await api.get<BookingResponse>('/api/bookings', { params });

      if (response.data.success) {
        console.log('Successfully fetched bookings from API');
        return response.data.data as Booking[];
      } else {
        throw new Error(response.data.message || 'Failed to get bookings');
      }
    } catch (error: any) {
      console.error('Error getting bookings from API, falling back to mock data:', error);
      console.warn('Using mock bookings data as fallback');

      // Fallback to mock data if API call fails
      if (status) {
        return mockBookings.filter(booking => booking.status === status);
      }

      return [...mockBookings];
    }
  },

  /**
   * Get booking by ID - connects to real backend API
   */
  getBooking: async (id: string): Promise<Booking> => {
    try {
      console.log('Fetching booking by ID from API:', id);

      // Call the real API endpoint
      const response = await api.get<BookingResponse>(`/api/bookings/${id}`);

      if (response.data.success) {
        console.log('Successfully fetched booking from API');
        return response.data.data as Booking;
      } else {
        throw new Error(response.data.message || 'Booking not found');
      }
    } catch (error: any) {
      console.error('Error getting booking from API, falling back to mock data:', error);
      console.warn('Using mock booking data as fallback');

      // Fallback to mock data if API call fails
      const booking = mockBookings.find(booking => booking.id === id);

      if (!booking) {
        throw new Error('Booking not found');
      }

      return {...booking};
    }
  },

  /**
   * Create a new booking - connects to real backend API
   */
  createBooking: async (data: CreateBookingData): Promise<Booking> => {
    try {
      console.log('Creating booking with API:', data);

      // Format the data for the backend
      const formattedData = {
        serviceId: data.serviceId,
        // Ensure we have a valid UUID for addressId - don't use 'default' as it's not a valid UUID
        addressId: data.addressId && data.addressId !== 'undefined' ? data.addressId : null,
        scheduledDate: formatDateForBackend(data.date),
        scheduledTime: formatTimeForBackend(data.startTime),
        paymentMethod: formatPaymentMethodForBackend(data.paymentMethod || 'pay_later'),
        notes: data.notes || '',
        paymentId: data.paymentId,
        frequency: 'ONE_TIME',
        // Include total amount which is required by the backend
        totalAmount: data.total || data.price || 0
      };

      // If addressId is null, we need to handle this case by getting the user's default address
      // or creating a new one before proceeding
      if (!formattedData.addressId) {
        console.warn('No valid addressId provided, attempting to use default address');
        try {
          // Try to get user's addresses
          const addressResponse = await api.get('/api/addresses');
          if (addressResponse.data.success && addressResponse.data.data.length > 0) {
            // Use the first address or primary address if available
            const primaryAddress = addressResponse.data.data.find((addr: any) => addr.isPrimary);
            formattedData.addressId = primaryAddress ? primaryAddress.id : addressResponse.data.data[0].id;
            console.log('Using existing address:', formattedData.addressId);
          } else {
            throw new Error('No addresses found for user');
          }
        } catch (addressError) {
          console.error('Error getting user addresses:', addressError);
          throw new Error('A valid address is required for booking. Please add an address first.');
        }
      }

      console.log('Creating booking with API:', formattedData);

      // Add a short delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Call the real API endpoint
      const response = await api.post<BookingResponse>('/api/bookings', formattedData);

      if (response.data.success) {
        console.log('Successfully created booking with API');
        return response.data.data as Booking;
      } else {
        throw new Error(response.data.message || 'Failed to create booking');
      }
    } catch (error: any) {
      console.error('Error creating booking with API, falling back to mock implementation:', error);
      console.warn('Using mock booking creation as fallback');

      // Fallback to mock implementation if API call fails
      // Generate a new booking ID
      const bookingId = `BK-${Math.floor(Math.random() * 100000)}`;

      // Create a new booking object
      const newBooking: Booking = {
        id: bookingId,
        serviceId: data.serviceId,
        serviceName: 'Service ' + data.serviceId, // Mock service name
        serviceImage: 'https://picsum.photos/id/26/600/300', // Mock image
        date: data.date,
        time: data.startTime,
        duration: '2 hours', // Mock duration
        status: BookingStatus.PENDING,
        price: 250, // Mock price
        address: {
          id: data.addressId,
          label: 'Address ' + data.addressId,
          address: 'Mock address'
        },
        provider: data.providerId ? {
          id: data.providerId,
          name: 'Provider ' + data.providerId,
          image: 'https://randomuser.me/api/portraits/men/32.jpg',
          phone: '+123456789',
          rating: 4.5
        } : null,
        paymentMethod: data.paymentMethod || 'PENDING',
        paymentStatus: 'PENDING',
        notes: data.notes || ''
      };

      return newBooking;
    }
  },

  /**
   * Update booking status - connects to real backend API
   */
  updateBookingStatus: async (id: string, data: UpdateBookingStatusData): Promise<Booking> => {
    try {
      console.log('Updating booking status with API:', id, data.status);

      // Call the real API endpoint
      const response = await api.patch<BookingResponse>(`/api/bookings/${id}/status`, data);

      if (response.data.success) {
        console.log('Successfully updated booking status with API');
        return response.data.data as Booking;
      } else {
        throw new Error(response.data.message || 'Failed to update booking status');
      }
    } catch (error: any) {
      console.error('Error updating booking status with API, falling back to mock implementation:', error);
      console.warn('Using mock booking status update as fallback');

      // Fallback to mock implementation if API call fails
      // Find the booking in the mock data
      const booking = await bookingService.getBooking(id);

      // Update the status
      booking.status = data.status;

      return booking;
    }
  },

  /**
   * Auto-assign provider to booking - connects to real backend API
   */
  autoAssignProvider: async (data: AutoAssignProviderData): Promise<Booking> => {
    try {
      console.log('Auto-assigning provider to booking with API:', data.bookingId);

      // Call the real API endpoint
      const response = await api.post<BookingResponse>(`/api/bookings/${data.bookingId}/auto-assign`, {});

      if (response.data.success) {
        console.log('Successfully auto-assigned provider with API');
        return response.data.data as Booking;
      } else {
        throw new Error(response.data.message || 'Failed to auto-assign provider');
      }
    } catch (error: any) {
      console.error('Error auto-assigning provider with API, falling back to mock implementation:', error);
      console.warn('Using mock auto-assign as fallback');

      // Fallback to mock implementation if API call fails
      // Find the booking in the mock data
      const booking = await bookingService.getBooking(data.bookingId);

      // Assign a mock provider
      booking.provider = {
        id: 'prov1',
        name: 'Mariama Jallow',
        image: 'https://randomuser.me/api/portraits/women/44.jpg',
        phone: '+220 7123456',
        rating: 4.9
      };

      return booking;
    }
  },

  /**
   * Get upcoming bookings - connects to real backend API
   */
  getUpcomingBookings: async (): Promise<Booking[]> => {
    try {
      console.log('Fetching upcoming bookings from API');

      // Call the real API endpoint
      const response = await api.get<BookingResponse>('/api/bookings/upcoming');

      if (response.data.success) {
        console.log('Successfully fetched upcoming bookings from API');
        return response.data.data as Booking[];
      } else {
        throw new Error(response.data.message || 'Failed to get upcoming bookings');
      }
    } catch (error: any) {
      console.error('Error getting upcoming bookings from API, falling back to mock data:', error);
      console.warn('Using mock upcoming bookings data as fallback');

      // Fallback to mock data if API call fails
      return mockBookings.filter(booking =>
        booking.status === BookingStatus.PENDING ||
        booking.status === BookingStatus.CONFIRMED
      );
    }
  },

  /**
   * Get past bookings - connects to real backend API
   */
  getPastBookings: async (): Promise<Booking[]> => {
    try {
      console.log('Fetching past bookings from API');

      // Call the real API endpoint
      const response = await api.get<BookingResponse>('/api/bookings/past');

      if (response.data.success) {
        console.log('Successfully fetched past bookings from API');
        return response.data.data as Booking[];
      } else {
        throw new Error(response.data.message || 'Failed to get past bookings');
      }
    } catch (error: any) {
      console.error('Error getting past bookings from API, falling back to mock data:', error);
      console.warn('Using mock past bookings data as fallback');

      // Fallback to mock data if API call fails
      return mockBookings.filter(booking =>
        booking.status === BookingStatus.COMPLETED ||
        booking.status === BookingStatus.CANCELLED ||
        booking.status === BookingStatus.REJECTED
      );
    }
  },

  /**
   * Cancel booking - connects to real backend API
   */
  cancelBooking: async (id: string, reason?: string): Promise<Booking> => {
    try {
      console.log('Cancelling booking with API:', id, reason ? `Reason: ${reason}` : '');

      // Call the real API endpoint
      const response = await api.patch<BookingResponse>(`/api/bookings/${id}/status`, {
        status: BookingStatus.CANCELLED,
        reason
      });

      if (response.data.success) {
        console.log('Successfully cancelled booking with API');
        return response.data.data as Booking;
      } else {
        throw new Error(response.data.message || 'Failed to cancel booking');
      }
    } catch (error: any) {
      console.error('Error cancelling booking with API, falling back to mock implementation:', error);
      console.warn('Using mock booking cancellation as fallback');

      // Fallback to mock implementation if API call fails
      // Find the booking in the mock data
      const booking = await bookingService.getBooking(id);

      // Update the status
      booking.status = BookingStatus.CANCELLED;
      if (reason) {
        booking.cancellationReason = reason;
      }

      return booking;
    }
  },
};

export default bookingService;
