import api from '../config';
import { Review } from '../../types/booking';

// Types
export interface ReviewResponse {
  success: boolean;
  data: Review | Review[];
  message?: string;
}

export interface CreateReviewData {
  bookingId: string;
  rating: number;
  comment?: string;
}

// Review service methods
const reviewService = {
  /**
   * Get reviews for a provider
   */
  getProviderReviews: async (providerId: string): Promise<Review[]> => {
    try {
      const response = await api.get<ReviewResponse>(`/reviews?providerId=${providerId}`);
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to get provider reviews');
      }
      
      return response.data.data as Review[];
    } catch (error: any) {
      throw error.response?.data || { success: false, error: 'Failed to get provider reviews' };
    }
  },
  
  /**
   * Create a new review
   */
  createReview: async (data: CreateReviewData): Promise<Review> => {
    try {
      const response = await api.post<ReviewResponse>('/reviews', data);
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to create review');
      }
      
      return response.data.data as Review;
    } catch (error: any) {
      throw error.response?.data || { success: false, error: 'Failed to create review' };
    }
  },
};

export default reviewService;
