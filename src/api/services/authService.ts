import * as SecureStore from 'expo-secure-store';
import { UserRole } from '../../types/user';
import { API_URL } from '../config';
import * as authStorage from '../../utils/authStorage';

// Data types
export interface LoginData {
  email?: string;
  password?: string;
  phone?: string;
  role?: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email?: string;
  password?: string;
  phone?: string;
  role: UserRole;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    user?: {
      id: string;
      email?: string;
      phone?: string;
      firstName: string;
      lastName: string;
      role: UserRole;
    };
    token?: string;
    tokens?: {
      accessToken: string;
      refreshToken: string;
    };
    requiresVerification?: boolean;
    verificationReason?: 'EMAIL_NOT_VERIFIED' | 'PHONE_NOT_VERIFIED' | 'TWO_FACTOR';
    email?: string;
    phone?: string;
    otp?: string; // For development only
  };
  message?: string;
  error?: string;
  user?: any; // For direct access
  tokens?: any; // For direct access
  isOtpRequired?: boolean; // For phone-based OTP
  otp?: string; // For development only
}

// Auth service methods - UI only, no backend calls
const authService = {
  /**
   * Register a new user - Makes a real API call to the backend
   */
  register: async (data: RegisterData): Promise<AuthResponse> => {
    try {
      console.log('Registering user with:', { ...data, password: '[MASKED]' });

      // Ensure the role is using the proper UserRole enum
      let userRole = data.role;

      // If the role is a string, convert it to the proper enum
      if (typeof userRole === 'string') {
        const lowerRole = userRole.toLowerCase();
        if (lowerRole === 'provider') {
          userRole = UserRole.PROVIDER;
        } else {
          userRole = UserRole.CUSTOMER;
        }
      }

      // Also check for stored role in userRole (from RoleSelectionScreen)
      try {
        const storedRole = await SecureStore.getItemAsync('userRole');
        if (storedRole && storedRole.toLowerCase() === 'provider') {
          userRole = UserRole.PROVIDER;
          console.log('Using provider role from storage for registration');
        }
      } catch (error) {
        console.error('Error checking stored role:', error);
      }

      console.log('Making registration API call to:', `${API_URL}/api/auth/register`);

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      // Make the API call to register the user
      const response = await fetch(`${API_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone,
          role: userRole
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // Check if response is ok before trying to parse JSON
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Registration API error:', response.status, errorText);
        throw new Error(`Registration failed with status ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      console.log('Registration API response:', responseData);

      // Store the role for future use
      await SecureStore.setItemAsync('userRoleEnum', userRole);

      // Store the pending user data for use after verification
      if (responseData.data?.user) {
        await SecureStore.setItemAsync('pending_user_data', JSON.stringify(responseData.data.user));
      }

      if (responseData.data?.token) {
        await SecureStore.setItemAsync('pending_token', responseData.data.token);
      }

      // Return the response with requiresVerification flag
      return {
        success: true,
        data: {
          ...responseData.data,
          requiresVerification: true,
          verificationReason: 'EMAIL_NOT_VERIFIED',
          email: data.email
        },
        message: responseData.message || 'Registration successful. Please verify your email.'
      };
    } catch (error: any) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message || 'Registration failed. Please try again.'
      };
    }
  },

  /**
   * Login a user - Makes a real API call to the backend
   */
  login: async (data: LoginData): Promise<AuthResponse> => {
    try {
      // Determine login type based on provided data
      const isPhoneLogin = !!data.phone;
      const isEmailLogin = !!data.email && !!data.password;

      if (isPhoneLogin) {
        console.log('Logging in with phone:', { phone: data.phone, role: data.role });

        // Store credentials for future use if remember me is enabled
        if (data.rememberMe) {
          await SecureStore.setItemAsync('cached_phone', data.phone);
          await SecureStore.setItemAsync('remember_me', 'true');
        }
      } else if (isEmailLogin) {
        console.log('Logging in with email:', { email: data.email, passwordLength: data.password?.length, role: data.role });

        // Store credentials for future use if remember me is enabled
        if (data.rememberMe) {
          await SecureStore.setItemAsync('cached_email', data.email);
          await SecureStore.setItemAsync('remember_me', 'true');
        }
      } else {
        throw new Error('Invalid login data: must provide either phone or email+password');
      }

      if (!data.rememberMe) {
        await SecureStore.deleteItemAsync('remember_me');
      }

      // Get the user role from data or storage or default to CUSTOMER
      let role = data.role || 'CUSTOMER';
      if (!data.role) {
        try {
          const storedRole = await SecureStore.getItemAsync('userRole');
          if (storedRole === 'provider') {
            role = 'PROVIDER';
          }
        } catch (error) {
          console.log('Error getting stored role, defaulting to CUSTOMER');
        }
      }

      console.log(`Logging in with role: ${role}`);

      // Prepare request body based on login type
      const requestBody: any = { role };

      if (isPhoneLogin) {
        requestBody.phone = data.phone;
      } else {
        requestBody.email = data.email;
        requestBody.password = data.password;
      }

      // Make the API call to login the user
      const response = await fetch(`${API_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const responseData = await response.json();

      if (!response.ok) {
        // Provide more specific error messages based on status code
        if (response.status === 401) {
          throw new Error(isPhoneLogin ? 'Invalid phone number. Please try again.' : 'Invalid email or password. Please try again.');
        } else if (response.status === 403) {
          throw new Error('Your account has been deactivated. Please contact support.');
        } else {
          throw new Error(responseData.message || 'Login failed');
        }
      }

      // For phone-based login, check if OTP is required
      if (isPhoneLogin && responseData.isOtpRequired) {
        console.log('Phone login requires OTP verification');
        return {
          success: true,
          isOtpRequired: true,
          otp: responseData.otp, // For development only
          message: 'OTP verification required'
        };
      }

      // For email login, check if verification is required
      if (isEmailLogin && responseData.data?.requiresVerification) {
        return {
          success: true,
          data: {
            ...responseData.data,
            requiresVerification: true,
            verificationReason: responseData.data.verificationReason || 'EMAIL_NOT_VERIFIED',
            email: data.email
          },
          message: responseData.message || 'Verification required'
        };
      }

      // Handle direct user and tokens in response (new format)
      const user = responseData.user || responseData.data?.user;
      const tokens = responseData.tokens || responseData.data?.tokens || responseData.data?.token;

      // Store the user data and tokens using the auth storage service
      if (tokens) {
        console.log('Storing tokens in secure storage');
        if (typeof tokens === 'string') {
          // Legacy token format
          await authStorage.saveAuthTokens(tokens);
        } else if (tokens.accessToken && tokens.refreshToken) {
          // New token format
          await authStorage.saveAuthTokens(tokens.accessToken, tokens.refreshToken);
        } else {
          console.warn('Unexpected token format:', tokens);
        }
      } else {
        console.warn('No tokens received in login response');
      }

      if (user) {
        console.log('Storing user data in secure storage');
        await authStorage.saveUserData(user);

        // Store the user role
        if (user.role) {
          await authStorage.saveUserRole(user.role);
        }
      }

      // Set authentication status
      await authStorage.saveAuthState(true);

      // Return the response
      return {
        success: true,
        user,
        tokens,
        data: responseData.data,
        message: responseData.message || 'Login successful'
      };
    } catch (error: any) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.message || 'Login failed. Please try again.'
      };
    }
  },

  /**
   * Offline login with cached credentials - UI only, no backend calls
   */
  offlineLogin: async (data: LoginData): Promise<AuthResponse> => {
    // Check for specific cleaner credentials even in offline mode
    if (data.email === '<EMAIL>' && data.password === 'M12345a@') {
      console.log('Recognized cleaner credentials in offline mode');

      // Create a cleaner user with provider role
      const cleanerUser = {
        id: 'cleaner-offline-' + Date.now(),
        email: '<EMAIL>',
        firstName: 'Momodou',
        lastName: 'Jallow',
        role: UserRole.PROVIDER
      };

      // Generate a mock token
      const mockToken = 'cleaner-offline-token-' + Date.now();

      // Store the cleaner user and token using the auth storage service
      await authStorage.saveAuthTokens(mockToken);
      await authStorage.saveUserData(cleanerUser);
      await authStorage.saveUserRole(UserRole.PROVIDER);
      await authStorage.saveAuthState(true);

      console.log('Set authentication status to true for offline cleaner login');

      console.log('Created offline cleaner user with role:', cleanerUser.role);

      // Return a successful response
      return {
        success: true,
        data: {
          user: cleanerUser,
          token: mockToken,
          requiresVerification: false
        },
        message: 'Offline cleaner login successful'
      };
    }

    // For other users, use the regular login method
    return authService.login(data);
  },

  /**
   * Verify token - UI only, no backend calls
   */
  verifyToken: async (): Promise<boolean> => {
    try {
      const token = await SecureStore.getItemAsync('auth_token');
      console.log('Verifying token (UI only):', token ? 'Token exists' : 'No token');

      if (!token) {
        console.log('No token found, returning false');
        return false;
      }

      // Check if we have user data in storage
      const userData = await SecureStore.getItemAsync('user_data');
      if (userData) {
        console.log('User data found in secure storage');
        return true;
      }

      console.log('No user data found, returning false');
      return false;
    } catch (error: any) {
      console.error('Token verification error:', error.message || error);
      return false;
    }
  },

  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    // Clear all auth-related data using the auth storage service
    await authStorage.clearAuthData();

    console.log('API service logout completed: All auth data cleared');
  },

  /**
   * Verify OTP for phone or email verification
   */
  verifyOtp: async (data: { phone?: string; email?: string; otp: string; purpose: string }): Promise<AuthResponse> => {
    try {
      console.log('Verifying OTP:', { ...data, otp: '[MASKED]' });

      // Make the API call to verify OTP
      const response = await fetch(`${API_URL}/api/auth/verify-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.message || 'OTP verification failed');
      }

      // Handle successful verification
      if (responseData.tokens && responseData.user) {
        // Store the user data and tokens
        await authStorage.saveAuthTokens(responseData.tokens.accessToken, responseData.tokens.refreshToken);
        await authStorage.saveUserData(responseData.user);
        await authStorage.saveAuthState(true);

        // Store the user role
        if (responseData.user.role) {
          await authStorage.saveUserRole(responseData.user.role);
        }

        console.log('OTP verification successful, user authenticated');
      }

      return {
        success: true,
        user: responseData.user,
        tokens: responseData.tokens,
        data: responseData,
        message: responseData.message || 'OTP verification successful'
      };
    } catch (error: any) {
      console.error('OTP verification error:', error);
      return {
        success: false,
        error: error.message || 'OTP verification failed. Please try again.'
      };
    }
  },

  /**
   * Get current user data
   */
  getCurrentUser: async () => {
    try {
      // Get user data from auth storage service
      const user = await authStorage.getUserData();

      if (!user) {
        return null;
      }

      // Check if we need to convert the role format
      // This handles the case where role might be stored as lowercase "provider" or "customer"
      if (user && user.role) {
        // If role is lowercase, convert it to the proper UserRole enum value
        if (typeof user.role === 'string') {
          const lowerRole = user.role.toLowerCase();

          if (lowerRole === 'provider') {
            user.role = UserRole.PROVIDER;
            console.log('Converted lowercase provider role to enum:', user.role);
          } else if (lowerRole === 'customer') {
            user.role = UserRole.CUSTOMER;
            console.log('Converted lowercase customer role to enum:', user.role);
          }
        }
      }

      // Also check for userRole in storage
      const storedRole = await authStorage.getUserRole();
      if (storedRole && (!user.role || user.role === 'CUSTOMER')) {
        if (storedRole === UserRole.PROVIDER) {
          user.role = UserRole.PROVIDER;
          console.log('Updated user role from storage:', user.role);
        }
      }

      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },
};

export default authService;
