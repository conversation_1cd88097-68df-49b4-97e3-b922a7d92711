import * as SecureStore from 'expo-secure-store';
import api from '../config';
import { UserRole } from '../../types/user';

// Data types
export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
}

export interface AuthResponse {
  success: boolean;
  data: {
    user?: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      role: UserRole;
    };
    token?: string;
    requiresVerification?: boolean;
    verificationReason?: 'EMAIL_NOT_VERIFIED' | 'TWO_FACTOR';
    email?: string;
  };
  message?: string;
  error?: string;
}

// Auth service methods
const authService = {
  /**
   * Register a new user
   */
  register: async (data: RegisterData): Promise<AuthResponse> => {
    try {
      const response = await api.post<AuthResponse>('/auth/register', data);

      // Store auth data on successful registration
      if (response.data.success && response.data.data.token) {
        await SecureStore.setItemAsync('auth_token', response.data.data.token);
        await SecureStore.setItemAsync('user_data', JSON.stringify(response.data.data.user));
      }

      return response.data;
    } catch (error: any) {
      console.error('Registration error details:', error);
      if (error.response?.data) {
        return error.response.data;
      } else {
        return { success: false, error: 'Registration failed' };
      }
    }
  },

  /**
   * Login a user
   */
  login: async (data: LoginData): Promise<AuthResponse> => {
    try {
      const response = await api.post<AuthResponse>('/auth/login', data);

      // Store auth data on successful login
      if (response.data.success && response.data.data.token) {
        await SecureStore.setItemAsync('auth_token', response.data.data.token);
        await SecureStore.setItemAsync('user_data', JSON.stringify(response.data.data.user));
      }

      return response.data;
    } catch (error: any) {
      console.error('Login error details:', error);
      if (error.response?.data) {
        return error.response.data;
      } else {
        return { success: false, error: 'Login failed' };
      }
    }
  },

  /**
   * Verify token
   */
  verifyToken: async (): Promise<boolean> => {
    try {
      const token = await SecureStore.getItemAsync('auth_token');

      if (!token) {
        return false;
      }

      const response = await api.get<AuthResponse>('/auth/verify');

      // Update user data if token is valid
      if (response.data.success && response.data.data.user) {
        await SecureStore.setItemAsync('user_data', JSON.stringify(response.data.data.user));
        return true;
      }

      return false;
    } catch (error) {
      // Clear auth data if token verification fails
      await SecureStore.deleteItemAsync('auth_token');
      await SecureStore.deleteItemAsync('user_data');
      return false;
    }
  },

  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    await SecureStore.deleteItemAsync('auth_token');
    await SecureStore.deleteItemAsync('user_data');
  },

  /**
   * Get current user data
   */
  getCurrentUser: async () => {
    const userData = await SecureStore.getItemAsync('user_data');
    return userData ? JSON.parse(userData) : null;
  },
};

export default authService;
