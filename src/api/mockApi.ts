import * as SecureStore from 'expo-secure-store';
import { users, services, addresses, providers, bookings, delay, generateSuccessResponse, generateErrorResponse, generateId } from '../data/mockData';
import { UserRole } from '../types/user';
import { BookingStatus } from '../types/booking';

// Mock API implementation
const mockApi = {
  // Auth endpoints
  auth: {
    async register(data: any) {
      await delay(1000); // Simulate network delay
      
      // Check if email already exists
      const existingUser = users.find(user => user.email === data.email);
      if (existingUser) {
        return generateErrorResponse('Email already registered');
      }
      
      // Create new user
      const newUser = {
        id: generateId(),
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        role: data.role as UserRole,
        phone: data.phone || '',
      };
      
      // In a real app, we would add the user to the database
      // For mock purposes, we'll just return success
      
      // Generate token (in a real app this would be a JWT)
      const token = `mock-token-${Date.now()}`;
      
      // Store in secure storage
      await SecureStore.setItemAsync('auth_token', token);
      await SecureStore.setItemAsync('user_data', JSON.stringify(newUser));
      
      return generateSuccessResponse({
        user: newUser,
        token,
      });
    },
    
    async login(data: any) {
      await delay(1000); // Simulate network delay
      
      // For demo purposes, accept any credentials with valid format
      if (!data.email || !data.password) {
        return generateErrorResponse('Email and password are required');
      }
      
      // Find user by email (in a real app, we would check password)
      const user = users.find(user => user.email === data.email);
      
      // For demo, create a mock user if not found
      const mockUser = user || {
        id: generateId(),
        email: data.email,
        firstName: 'Demo',
        lastName: 'User',
        role: 'CUSTOMER' as UserRole,
        phone: '',
      };
      
      // Generate token
      const token = `mock-token-${Date.now()}`;
      
      // Store in secure storage
      await SecureStore.setItemAsync('auth_token', token);
      await SecureStore.setItemAsync('user_data', JSON.stringify(mockUser));
      await SecureStore.setItemAsync('cached_email', data.email);
      await SecureStore.setItemAsync('cached_password', data.password);
      
      return generateSuccessResponse({
        user: mockUser,
        token,
      });
    },
    
    async verifyOtp(data: any) {
      await delay(1000); // Simulate network delay
      
      // For demo purposes, accept any OTP
      if (!data.email || !data.code) {
        return generateErrorResponse('Email and verification code are required');
      }
      
      return generateSuccessResponse({
        verified: true,
      });
    },
    
    async completeLogin(data: any) {
      await delay(1000); // Simulate network delay
      
      // For demo purposes, accept any OTP
      if (!data.email || !data.code) {
        return generateErrorResponse('Email and verification code are required');
      }
      
      // Find user by email
      const user = users.find(user => user.email === data.email);
      
      // For demo, create a mock user if not found
      const mockUser = user || {
        id: generateId(),
        email: data.email,
        firstName: 'Demo',
        lastName: 'User',
        role: 'CUSTOMER' as UserRole,
        phone: '',
      };
      
      // Generate token
      const token = `mock-token-${Date.now()}`;
      
      // Store in secure storage
      await SecureStore.setItemAsync('auth_token', token);
      await SecureStore.setItemAsync('user_data', JSON.stringify(mockUser));
      
      return generateSuccessResponse({
        user: mockUser,
        token,
      });
    },
    
    async sendOtp(data: any) {
      await delay(1000); // Simulate network delay
      
      if (!data.email) {
        return generateErrorResponse('Email is required');
      }
      
      return generateSuccessResponse({
        sent: true,
        email: data.email,
      });
    },
  },
  
  // Service endpoints
  services: {
    async getServices() {
      await delay(500); // Simulate network delay
      return generateSuccessResponse(services);
    },
    
    async getServiceById(id: string) {
      await delay(500); // Simulate network delay
      
      const service = services.find(service => service.id === id);
      if (!service) {
        return generateErrorResponse('Service not found');
      }
      
      // Add providers to service
      const serviceWithProviders = {
        ...service,
        providers: providers.slice(0, 3), // Just add a few providers
      };
      
      return generateSuccessResponse(serviceWithProviders);
    },
  },
  
  // Address endpoints
  addresses: {
    async getAddresses() {
      await delay(500); // Simulate network delay
      return generateSuccessResponse(addresses);
    },
    
    async createAddress(data: any) {
      await delay(1000); // Simulate network delay
      
      const newAddress = {
        id: generateId(),
        label: data.label,
        address: data.address,
        area: data.area || '',
        isPrimary: data.isPrimary || false,
        latitude: data.latitude,
        longitude: data.longitude,
      };
      
      return generateSuccessResponse(newAddress);
    },
    
    async updateAddress(id: string, data: any) {
      await delay(1000); // Simulate network delay
      
      const address = addresses.find(addr => addr.id === id);
      if (!address) {
        return generateErrorResponse('Address not found');
      }
      
      const updatedAddress = {
        ...address,
        ...data,
      };
      
      return generateSuccessResponse(updatedAddress);
    },
    
    async deleteAddress(id: string) {
      await delay(1000); // Simulate network delay
      return generateSuccessResponse({ deleted: true });
    },
  },
  
  // Booking endpoints
  bookings: {
    async getBookings(status?: BookingStatus) {
      await delay(500); // Simulate network delay
      
      let filteredBookings = bookings;
      if (status) {
        filteredBookings = bookings.filter(booking => booking.status === status);
      }
      
      return generateSuccessResponse(filteredBookings);
    },
    
    async getBookingById(id: string) {
      await delay(500); // Simulate network delay
      
      const booking = bookings.find(booking => booking.id === id);
      if (!booking) {
        return generateErrorResponse('Booking not found');
      }
      
      return generateSuccessResponse(booking);
    },
    
    async createBooking(data: any) {
      await delay(1500); // Simulate network delay
      
      // Validate required fields
      if (!data.serviceId || !data.addressId || !data.date || !data.startTime) {
        return generateErrorResponse('Missing required booking information');
      }
      
      // Get service details
      const service = services.find(service => service.id === data.serviceId);
      if (!service) {
        return generateErrorResponse('Service not found');
      }
      
      // Get address details
      const address = addresses.find(addr => addr.id === data.addressId);
      if (!address) {
        return generateErrorResponse('Address not found');
      }
      
      // Create new booking
      const newBooking = {
        id: `BK-${Math.floor(Math.random() * 100000)}`,
        serviceId: data.serviceId,
        serviceName: service.title,
        serviceImage: service.image,
        date: data.date,
        time: data.startTime,
        duration: service.duration,
        status: 'PENDING' as BookingStatus,
        price: service.price,
        address: {
          id: address.id,
          label: address.label,
          address: address.address,
        },
        provider: data.providerId ? providers.find(p => p.id === data.providerId) : null,
        paymentMethod: 'PENDING',
        paymentStatus: 'PENDING',
        notes: data.notes || '',
      };
      
      return generateSuccessResponse(newBooking);
    },
    
    async updateBookingStatus(id: string, data: any) {
      await delay(1000); // Simulate network delay
      
      const booking = bookings.find(booking => booking.id === id);
      if (!booking) {
        return generateErrorResponse('Booking not found');
      }
      
      const updatedBooking = {
        ...booking,
        status: data.status as BookingStatus,
      };
      
      return generateSuccessResponse(updatedBooking);
    },
  },
  
  // Payment endpoints
  payments: {
    async processPayment(data: any) {
      await delay(2000); // Simulate network delay
      
      // Validate required fields
      if (!data.bookingId || !data.paymentMethod || !data.amount) {
        return generateErrorResponse('Missing required payment information');
      }
      
      // For mobile money, require receipt image
      if (data.paymentMethod === 'MOBILE_MONEY' && !data.receiptImage) {
        return generateErrorResponse('Receipt image is required for mobile money payments');
      }
      
      return generateSuccessResponse({
        id: generateId(),
        bookingId: data.bookingId,
        amount: data.amount,
        method: data.paymentMethod,
        status: 'COMPLETED',
        timestamp: new Date().toISOString(),
      });
    },
    
    async uploadReceipt(bookingId: string, receiptImage: string) {
      await delay(1500); // Simulate network delay
      
      if (!bookingId || !receiptImage) {
        return generateErrorResponse('Booking ID and receipt image are required');
      }
      
      return generateSuccessResponse({
        uploaded: true,
        url: 'https://example.com/receipts/mock-receipt.jpg',
      });
    },
  },
  
  // Provider endpoints
  providers: {
    async getTopRatedProviders(limit: number = 5) {
      await delay(500); // Simulate network delay
      return generateSuccessResponse(providers.slice(0, limit));
    },
    
    async getProviderById(id: string) {
      await delay(500); // Simulate network delay
      
      const provider = providers.find(provider => provider.id === id);
      if (!provider) {
        return generateErrorResponse('Provider not found');
      }
      
      return generateSuccessResponse(provider);
    },
  },
  
  // Profile endpoints
  profile: {
    async getProfile() {
      await delay(500); // Simulate network delay
      
      // Get user data from secure storage
      const userData = await SecureStore.getItemAsync('user_data');
      if (!userData) {
        return generateErrorResponse('User not found');
      }
      
      const user = JSON.parse(userData);
      return generateSuccessResponse(user);
    },
    
    async updateProfile(data: any) {
      await delay(1000); // Simulate network delay
      
      // Get user data from secure storage
      const userData = await SecureStore.getItemAsync('user_data');
      if (!userData) {
        return generateErrorResponse('User not found');
      }
      
      const user = JSON.parse(userData);
      const updatedUser = {
        ...user,
        ...data,
      };
      
      // Store updated user data
      await SecureStore.setItemAsync('user_data', JSON.stringify(updatedUser));
      
      return generateSuccessResponse(updatedUser);
    },
  },
  
  // Health check endpoint
  health: {
    async check() {
      await delay(200); // Simulate network delay
      return generateSuccessResponse({ status: 'ok' });
    },
  },
};

export default mockApi;
