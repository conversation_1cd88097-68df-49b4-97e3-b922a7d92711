import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// API base URL configuration
// For Expo Go on physical devices, use the local network IP address
// For emulators, use ******** (Android) or localhost (iOS)
// For web, use relative URLs

// Hardcoded IP for development - this is the most reliable approach for Expo Go
const LOCAL_IP = '************'; // Using the actual IP address of the development machine
const API_PORT = '3000';

// Define the API URL - this is used throughout the app
let baseUrl = `http://${LOCAL_IP}:${API_PORT}`;

// Log that we're using the local development server
console.log('Connecting to local development server at:', baseUrl);

// For different platforms
if (__DEV__) {
  // Always use the specified IP address for all platforms in development
  // This ensures consistent connectivity across devices
  console.log(`Using fixed IP address for all platforms: ${baseUrl}`);

  // For web only, use relative URL
  if (Platform.OS === 'web') {
    baseUrl = `/api`;
    console.log('Using web configuration:', baseUrl);
  }
}

// Export the API URL for use throughout the app
export const API_URL = baseUrl;

// Log the API URL for debugging
console.log('Using API URL:', API_URL);

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  timeout: 30000, // Increased timeout to 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Log request details for debugging
api.interceptors.request.use(request => {
  console.log('Starting API Request:', request.method?.toUpperCase(), request.baseURL + request.url);
  return request;
});

// Add request interceptor to add auth token to requests
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting token for request:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    console.log('API Response Success:', response.config.method?.toUpperCase(), response.config.url);
    return response;
  },
  async (error) => {
    // Log detailed error information
    console.error('API Response Error:', error.message);
    console.error('Request URL:', error.config?.baseURL + error.config?.url);
    console.error('Request Method:', error.config?.method?.toUpperCase());

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
      console.error('Headers:', JSON.stringify(error.response.headers, null, 2));
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received. Request details:', error.request);
      console.error('CONNECTION ERROR: Make sure the backend server is running at ' + API_URL);
      console.error('If using an emulator, ensure the IP address is correct (************)');
    }

    const originalRequest = error.config;

    // Add retry count if not present
    if (originalRequest && !originalRequest._retryCount) {
      originalRequest._retryCount = 0;
    }

    // Handle token expiration
    if (error.response?.status === 401 && !originalRequest._authRetry) {
      originalRequest._authRetry = true;
      console.log('Token expired. Attempting to refresh...');

      try {
        // Try to refresh the token
        const refreshToken = await SecureStore.getItemAsync('refreshToken');
        if (refreshToken) {
          console.log('Found refresh token, attempting to refresh...');

          try {
            const response = await axios.post(`${API_URL}/api/auth/refresh-token`, {
              refreshToken,
            });

            if (response.data.success) {
              console.log('Token refreshed successfully');

              // Save the new tokens
              await SecureStore.setItemAsync('accessToken', response.data.data.accessToken);
              await SecureStore.setItemAsync('refreshToken', response.data.data.refreshToken);

              // Update the Authorization header
              originalRequest.headers.Authorization = `Bearer ${response.data.data.accessToken}`;

              // Retry the original request
              return api(originalRequest);
            } else {
              console.error('Token refresh failed:', response.data.message);
              // Clear tokens if refresh fails
              await SecureStore.deleteItemAsync('accessToken');
              await SecureStore.deleteItemAsync('refreshToken');
            }
          } catch (refreshError: any) {
            console.error('Error during token refresh:', refreshError.message);
            // Clear tokens if refresh fails with an error
            await SecureStore.deleteItemAsync('accessToken');
            await SecureStore.deleteItemAsync('refreshToken');
          }
        } else {
          console.log('No refresh token found');
        }
      } catch (refreshError) {
        console.error('Error accessing refresh token:', refreshError);
      }
    }

    // Handle rate limiting (429) with automatic retry
    if (error.response?.status === 429 && originalRequest._retryCount < 3) {
      originalRequest._retryCount++;

      // Calculate exponential backoff delay: 1s, 2s, 4s
      const delay = 1000 * Math.pow(2, originalRequest._retryCount - 1);
      console.log(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount}/3)`);

      // Wait for the specified delay
      await new Promise(resolve => setTimeout(resolve, delay));

      // Retry the original request
      return api(originalRequest);
    }

    // Handle network errors with retry
    if ((!error.response || error.code === 'ECONNABORTED') && originalRequest && originalRequest._retryCount < 3) {
      originalRequest._retryCount++;

      // Calculate exponential backoff delay: 1s, 2s, 4s
      const delay = 1000 * Math.pow(2, originalRequest._retryCount - 1);
      console.log(`Network error. Retrying after ${delay}ms (attempt ${originalRequest._retryCount}/3)`);

      // Wait for the specified delay
      await new Promise(resolve => setTimeout(resolve, delay));

      // Retry the original request
      return api(originalRequest);
    }

    return Promise.reject(error);
  }
);

export default api;
