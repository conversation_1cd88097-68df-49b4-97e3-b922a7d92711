"use client"

import React, { useState, useEffect, useRef } from "react"
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  ActivityIndicator,
  Dimensions,
} from "react-native"
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
// No longer using SecureStore directly
import { useTheme } from "../context/ThemeContext"
// Not using auth or booking context directly
import { UserRole } from "../types/user"
import { authService } from "../api/services"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Button from "../components/Button"
import OtpVerificationModal from "../components/OtpVerificationModal"
import otpService from "../api/services/otpService"

type RegisterScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>
type RegisterScreenRouteProp = RouteProp<RootStackParamList, 'Register'>

const RegisterScreen = () => {
  const navigation = useNavigation<RegisterScreenNavigationProp>()
  const route = useRoute<RegisterScreenRouteProp>()
  const theme = useTheme()

  // Input refs for focus management
  const lastNameInputRef = useRef<TextInput>(null)
  const emailInputRef = useRef<TextInput>(null)
  const phoneInputRef = useRef<TextInput>(null)
  const passwordInputRef = useRef<TextInput>(null)
  const confirmPasswordInputRef = useRef<TextInput>(null)
  const tokenInputRef = useRef<TextInput>(null)

  // Form state
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [phone, setPhone] = useState("")
  const [token, setToken] = useState("")
  const [termsAccepted, setTermsAccepted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formTouched, setFormTouched] = useState(false)
  const [keyboardVisible, setKeyboardVisible] = useState(false)

  // Form validation errors
  const [firstNameError, setFirstNameError] = useState("")
  const [lastNameError, setLastNameError] = useState("")
  const [emailError, setEmailError] = useState("")
  const [phoneError, setPhoneError] = useState("")
  const [passwordError, setPasswordError] = useState("")
  const [confirmPasswordError, setConfirmPasswordError] = useState("")
  const [tokenError, setTokenError] = useState("")

  // Password strength
  const [passwordStrength, setPasswordStrength] = useState<'weak' | 'medium' | 'strong' | ''>('')

  // User role state - default to CUSTOMER if not provided
  const [userRole, setUserRole] = useState<UserRole>(
    route.params?.selectedRole || UserRole.CUSTOMER
  )

  // OTP verification modal state
  const [showOtpModal, setShowOtpModal] = useState(false)
  const [otpEmail, setOtpEmail] = useState("")

  // Get styles with the current theme
  const styles = getStyles(theme)

  // Get screen dimensions for responsive layout
  const { height } = Dimensions.get("window")

  // Handle keyboard events
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true)
      }
    )
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false)
      }
    )

    return () => {
      keyboardDidShowListener.remove()
      keyboardDidHideListener.remove()
    }
  }, [])

  // Set user role from route params if available
  useEffect(() => {
    if (route.params?.selectedRole) {
      setUserRole(route.params.selectedRole)
      console.log("User role set from route params:", route.params.selectedRole)
    }
  }, [route.params?.selectedRole])

  // Determine if token field should be shown
  const isCleanerRegistration = userRole === UserRole.PROVIDER

  // Validation functions
  const validateFirstName = (value: string): boolean => {
    if (!value.trim()) {
      setFirstNameError("First name is required")
      return false
    } else if (value.length < 2) {
      setFirstNameError("First name must be at least 2 characters")
      return false
    } else {
      setFirstNameError("")
      return true
    }
  }

  const validateLastName = (value: string): boolean => {
    if (!value.trim()) {
      setLastNameError("Last name is required")
      return false
    } else if (value.length < 2) {
      setLastNameError("Last name must be at least 2 characters")
      return false
    } else {
      setLastNameError("")
      return true
    }
  }

  const validateEmail = (value: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!value.trim()) {
      setEmailError("Email address is required")
      return false
    } else if (!emailRegex.test(value)) {
      setEmailError("Please enter a valid email address")
      return false
    } else {
      setEmailError("")
      return true
    }
  }

  const validatePhone = (value: string): boolean => {
    // Phone is optional, so empty is valid
    if (!value.trim()) {
      setPhoneError("")
      return true
    }

    // Simple phone validation - can be enhanced for specific formats
    const phoneRegex = /^\+?[0-9]{10,15}$/
    if (!phoneRegex.test(value.replace(/[\s-]/g, ''))) {
      setPhoneError("Please enter a valid phone number")
      return false
    } else {
      setPhoneError("")
      return true
    }
  }

  const checkPasswordStrength = (value: string): 'weak' | 'medium' | 'strong' | '' => {
    if (!value) return ''

    let strength = 0

    // Length check
    if (value.length >= 8) strength += 1

    // Character variety checks
    if (/[A-Z]/.test(value)) strength += 1
    if (/[a-z]/.test(value)) strength += 1
    if (/[0-9]/.test(value)) strength += 1
    if (/[^A-Za-z0-9]/.test(value)) strength += 1

    if (strength < 3) return 'weak'
    if (strength < 5) return 'medium'
    return 'strong'
  }

  const validatePassword = (value: string): boolean => {
    if (!value) {
      setPasswordError("Password is required")
      setPasswordStrength('')
      return false
    }

    const strength = checkPasswordStrength(value)
    setPasswordStrength(strength)

    if (value.length < 8) {
      setPasswordError("Password must be at least 8 characters")
      return false
    }

    // Check for password complexity
    const hasUppercase = /[A-Z]/.test(value)
    const hasLowercase = /[a-z]/.test(value)
    const hasNumber = /[0-9]/.test(value)
    const hasSpecial = /[^A-Za-z0-9]/.test(value)

    if (!hasUppercase || !hasLowercase || !hasNumber || !hasSpecial) {
      setPasswordError("Password must include uppercase, lowercase, number, and special character")
      return false
    }

    setPasswordError("")
    return true
  }

  const validateConfirmPassword = (value: string): boolean => {
    if (!value) {
      setConfirmPasswordError("Please confirm your password")
      return false
    } else if (value !== password) {
      setConfirmPasswordError("Passwords do not match")
      return false
    } else {
      setConfirmPasswordError("")
      return true
    }
  }

  const validateToken = (value: string): boolean => {
    if (isCleanerRegistration) {
      if (!value.trim()) {
        setTokenError("Approval code is required")
        return false
      }

      // Token validation - 6-8 alphanumeric characters
      const tokenRegex = /^[A-Z0-9]{6,8}$/
      if (!tokenRegex.test(value)) {
        setTokenError("Please enter a valid approval code (6-8 alphanumeric characters)")
        return false
      }
    }

    setTokenError("")
    return true
  }

  // Validate all form fields
  const validateForm = (): boolean => {
    setFormTouched(true)

    const isFirstNameValid = validateFirstName(firstName)
    const isLastNameValid = validateLastName(lastName)
    const isEmailValid = validateEmail(email)
    const isPhoneValid = validatePhone(phone)
    const isPasswordValid = validatePassword(password)
    const isConfirmPasswordValid = validateConfirmPassword(confirmPassword)
    const isTokenValid = validateToken(token)

    return (
      isFirstNameValid &&
      isLastNameValid &&
      isEmailValid &&
      isPhoneValid &&
      isPasswordValid &&
      isConfirmPasswordValid &&
      isTokenValid &&
      termsAccepted
    )
  }

  const handleRegister = async () => {
    // Validate all form fields
    if (!validateForm()) {
      // Show a summary of errors
      let errorMessage = "Please correct the following issues:\n";

      if (firstNameError) errorMessage += `• ${firstNameError}\n`;
      if (lastNameError) errorMessage += `• ${lastNameError}\n`;
      if (emailError) errorMessage += `• ${emailError}\n`;
      if (phoneError) errorMessage += `• ${phoneError}\n`;
      if (passwordError) errorMessage += `• ${passwordError}\n`;
      if (confirmPasswordError) errorMessage += `• ${confirmPasswordError}\n`;
      if (isCleanerRegistration && tokenError) errorMessage += `• ${tokenError}\n`;
      if (!termsAccepted) errorMessage += "• Please accept the terms and conditions\n";

      Alert.alert("Form Validation Failed", errorMessage);
      return;
    }

    // Dismiss keyboard
    Keyboard.dismiss();

    try {
      setIsLoading(true)

      // For cleaner registration, validate token (simulated)
      if (isCleanerRegistration) {
        // Simple validation: token must be 6-8 alphanumeric characters
        const tokenRegex = /^[A-Z0-9]{6,8}$/
        if (!tokenRegex.test(token)) {
          Alert.alert("Invalid Token", "Please enter a valid approval code (6-8 alphanumeric characters).")
          setIsLoading(false)
          return
        }
      }

      console.log('Attempting to register with:', {
        firstName,
        lastName,
        email,
        passwordLength: password?.length,
        phone: phone || undefined,
        role: userRole
      });

      try {
        // Call register API with timeout
        const registerPromise = authService.register({
          firstName,
          lastName,
          email,
          password,
          phone: phone || undefined,
          role: userRole
        });

        // Set a timeout for the registration process
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Registration timed out. Please try again.')), 15000);
        });

        // Race the registration against the timeout
        const response = await Promise.race([registerPromise, timeoutPromise]) as { success: boolean; error?: string; data?: any; message?: string };

        console.log('Registration response:', response);

        if (!response || !response.success) {
          Alert.alert("Registration Failed", response?.error || "Please try again.");
          setIsLoading(false);
          return;
        }

        console.log('Registration successful, sending OTP...');

        // Send OTP manually after successful registration
        try {
          const otpResponse = await otpService.sendOtp({
            email,
            purpose: 'REGISTER'
          });

          console.log('OTP send response:', otpResponse);

          if (!otpResponse.success) {
            Alert.alert(
              "Registration Successful",
              "Your account was created, but we couldn't send the verification code. Please try logging in."
            );
            setIsLoading(false);
            return;
          }

          // Show OTP verification modal
          setOtpEmail(email);
          setShowOtpModal(true);

          // Show alert with OTP information
          Alert.alert(
            "Verification Required",
            "A verification code has been sent to your email. Please check your inbox and enter the 4-digit code.",
            [{ text: "OK" }]
          );
        } catch (otpError) {
          console.error('Error sending OTP:', otpError);
          Alert.alert(
            "Registration Successful",
            "Your account was created, but we couldn't send the verification code. Please try logging in."
          );
          setIsLoading(false);
        }
      } catch (registerError) {
        console.error('Registration process error:', registerError);
        Alert.alert(
          "Registration Failed",
          registerError instanceof Error ? registerError.message : "Network error. Please check your connection and try again."
        );
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Registration error:", error)
      const errorMessage = error instanceof Error ? error.message : "Please try again later."
      Alert.alert("Registration Failed", errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <ScrollView
          contentContainerStyle={styles.contentContainer}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.navigate("Onboarding")}
              accessibilityLabel="Back button"
            >
              <Feather name="arrow-left" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <Text style={styles.title}>
            Create your {isCleanerRegistration ? "Cleaner" : "Customer"} Account
          </Text>
          <Text style={styles.subtitle}>
            {isCleanerRegistration
              ? "Join CleanConnect as a service provider and grow your business"
              : "Let's get you set up with a new account"}
          </Text>

          <View style={styles.form}>
            {/* First Name Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>First Name</Text>
              <View style={[
                styles.inputWrapper,
                formTouched && firstNameError ? { borderColor: theme.colors.error } : {}
              ]}>
                <Feather name="user" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Enter your first name"
                  placeholderTextColor={theme.colors.textLight}
                  value={firstName}
                  onChangeText={(text) => {
                    setFirstName(text)
                    if (formTouched) validateFirstName(text)
                  }}
                  onBlur={() => validateFirstName(firstName)}
                  returnKeyType="next"
                  onSubmitEditing={() => lastNameInputRef.current?.focus()}
                  blurOnSubmit={false}
                  accessibilityLabel="First name input field"
                />
              </View>
              {formTouched && firstNameError ? (
                <Text style={styles.errorText}>{firstNameError}</Text>
              ) : null}
            </View>

            {/* Last Name Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Last Name</Text>
              <View style={[
                styles.inputWrapper,
                formTouched && lastNameError ? { borderColor: theme.colors.error } : {}
              ]}>
                <Feather name="user" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  ref={lastNameInputRef}
                  style={styles.input}
                  placeholder="Enter your last name"
                  placeholderTextColor={theme.colors.textLight}
                  value={lastName}
                  onChangeText={(text) => {
                    setLastName(text)
                    if (formTouched) validateLastName(text)
                  }}
                  onBlur={() => validateLastName(lastName)}
                  returnKeyType="next"
                  onSubmitEditing={() => emailInputRef.current?.focus()}
                  blurOnSubmit={false}
                  accessibilityLabel="Last name input field"
                />
              </View>
              {formTouched && lastNameError ? (
                <Text style={styles.errorText}>{lastNameError}</Text>
              ) : null}
            </View>

            {/* Email Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Email</Text>
              <View style={[
                styles.inputWrapper,
                formTouched && emailError ? { borderColor: theme.colors.error } : {}
              ]}>
                <Feather name="mail" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  ref={emailInputRef}
                  style={styles.input}
                  placeholder="Enter your email"
                  placeholderTextColor={theme.colors.textLight}
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text)
                    if (formTouched) validateEmail(text)
                  }}
                  onBlur={() => validateEmail(email)}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  returnKeyType="next"
                  onSubmitEditing={() => phoneInputRef.current?.focus()}
                  blurOnSubmit={false}
                  accessibilityLabel="Email input field"
                />
              </View>
              {formTouched && emailError ? (
                <Text style={styles.errorText}>{emailError}</Text>
              ) : null}
            </View>

            {/* Phone Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Phone (Optional)</Text>
              <View style={[
                styles.inputWrapper,
                formTouched && phoneError ? { borderColor: theme.colors.error } : {}
              ]}>
                <Feather name="phone" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  ref={phoneInputRef}
                  style={styles.input}
                  placeholder="Enter your phone number"
                  placeholderTextColor={theme.colors.textLight}
                  value={phone}
                  onChangeText={(text) => {
                    setPhone(text)
                    if (formTouched) validatePhone(text)
                  }}
                  onBlur={() => validatePhone(phone)}
                  keyboardType="phone-pad"
                  returnKeyType="next"
                  onSubmitEditing={() => passwordInputRef.current?.focus()}
                  blurOnSubmit={false}
                  accessibilityLabel="Phone input field"
                />
              </View>
              {formTouched && phoneError ? (
                <Text style={styles.errorText}>{phoneError}</Text>
              ) : null}
            </View>

            {/* Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Password</Text>
              <View style={[
                styles.inputWrapper,
                formTouched && passwordError ? { borderColor: theme.colors.error } : {}
              ]}>
                <Feather name="lock" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  ref={passwordInputRef}
                  style={styles.input}
                  placeholder="Create a password"
                  placeholderTextColor={theme.colors.textLight}
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text)
                    validatePassword(text)
                    if (confirmPassword) validateConfirmPassword(confirmPassword)
                  }}
                  onBlur={() => validatePassword(password)}
                  secureTextEntry={!showPassword}
                  returnKeyType="next"
                  onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                  blurOnSubmit={false}
                  accessibilityLabel="Password input field"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                  accessibilityLabel={showPassword ? "Hide password" : "Show password"}
                >
                  <Feather
                    name={showPassword ? "eye-off" : "eye"}
                    size={20}
                    color={theme.colors.textLight}
                  />
                </TouchableOpacity>
              </View>
              {passwordStrength && (
                <View style={styles.passwordStrengthContainer}>
                  <View style={styles.strengthBarContainer}>
                    <View
                      style={[
                        styles.strengthBar,
                        passwordStrength === 'weak' ? styles.strengthBarWeak :
                        passwordStrength === 'medium' ? styles.strengthBarMedium :
                        passwordStrength === 'strong' ? styles.strengthBarStrong : {}
                      ]}
                    />
                  </View>
                  <Text style={styles.strengthText}>
                    Password strength: {passwordStrength}
                  </Text>
                </View>
              )}
              {formTouched && passwordError ? (
                <Text style={styles.errorText}>{passwordError}</Text>
              ) : null}
            </View>

            {/* Confirm Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Confirm Password</Text>
              <View style={[
                styles.inputWrapper,
                formTouched && confirmPasswordError ? { borderColor: theme.colors.error } : {}
              ]}>
                <Feather name="lock" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                <TextInput
                  ref={confirmPasswordInputRef}
                  style={styles.input}
                  placeholder="Confirm your password"
                  placeholderTextColor={theme.colors.textLight}
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text)
                    if (formTouched) validateConfirmPassword(text)
                  }}
                  onBlur={() => validateConfirmPassword(confirmPassword)}
                  secureTextEntry={!showConfirmPassword}
                  returnKeyType={isCleanerRegistration ? "next" : "done"}
                  onSubmitEditing={() => {
                    if (isCleanerRegistration) {
                      tokenInputRef.current?.focus()
                    } else {
                      Keyboard.dismiss()
                    }
                  }}
                  accessibilityLabel="Confirm password input field"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  accessibilityLabel={showConfirmPassword ? "Hide password" : "Show password"}
                >
                  <Feather
                    name={showConfirmPassword ? "eye-off" : "eye"}
                    size={20}
                    color={theme.colors.textLight}
                  />
                </TouchableOpacity>
              </View>
              {formTouched && confirmPasswordError ? (
                <Text style={styles.errorText}>{confirmPasswordError}</Text>
              ) : null}
            </View>

            {/* Approval Code Input (for cleaners only) */}
            {isCleanerRegistration && (
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Approval Code</Text>
                <View style={[
                  styles.inputWrapper,
                  formTouched && tokenError ? { borderColor: theme.colors.error } : {}
                ]}>
                  <Feather name="key" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                  <TextInput
                    ref={tokenInputRef}
                    style={styles.input}
                    placeholder="Enter your approval code"
                    placeholderTextColor={theme.colors.textLight}
                    value={token}
                    onChangeText={(text) => {
                      setToken(text)
                      if (formTouched) validateToken(text)
                    }}
                    onBlur={() => validateToken(token)}
                    autoCapitalize="characters"
                    returnKeyType="done"
                    accessibilityLabel="Approval code input field"
                  />
                </View>
                {formTouched && tokenError ? (
                  <Text style={styles.errorText}>{tokenError}</Text>
                ) : null}
                <Text style={styles.helperText}>
                  This code is given after approval by CleanConnect.
                </Text>

                <View style={styles.applyContainer}>
                  <Text style={styles.applyText}>Don&apos;t have a code?</Text>
                  <TouchableOpacity
                    onPress={() => navigation.navigate("CleanerApplicationForm")}
                    style={styles.applyButton}
                  >
                    <Text style={styles.applyButtonText}>Apply Now</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {/* Terms and Conditions */}
            <View style={styles.termsContainer}>
              <TouchableOpacity
                style={styles.checkboxContainer}
                onPress={() => setTermsAccepted(!termsAccepted)}
                accessibilityLabel={termsAccepted ? "Uncheck terms and conditions" : "Accept terms and conditions"}
                accessibilityRole="checkbox"
                accessibilityState={{ checked: termsAccepted }}
              >
                <View
                  style={[
                    styles.checkbox,
                    termsAccepted && styles.checkboxChecked,
                  ]}
                >
                  {termsAccepted && (
                    <Feather name="check" size={14} color={theme.colors.card} />
                  )}
                </View>
              </TouchableOpacity>
              <Text style={styles.termsText}>
                I agree to the{" "}
                <Text style={styles.termsLink}>Terms of Service</Text> and{" "}
                <Text style={styles.termsLink}>Privacy Policy</Text>
              </Text>
            </View>

            {/* Submit Button */}
            <Button
              title={isLoading ? "Creating Account..." : "Create Account"}
              variant="warning"
              fullWidth
              onPress={handleRegister}
              disabled={isLoading}
              loading={isLoading}
              accessibilityLabel="Create account button"
            />

            {/* Login Link */}
            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>
                Already have an account?{" "}
                <Text
                  style={styles.loginLink}
                  onPress={() => navigation.navigate("Login" as never)}
                >
                  Sign in
                </Text>
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* OTP Verification Modal */}
      <OtpVerificationModal
        visible={showOtpModal}
        onClose={() => setShowOtpModal(false)}
        email={otpEmail}
        purpose="REGISTER"
        userRole={userRole}
        returnTo={route.params?.returnTo}
        returnParams={route.params?.returnParams}
        onSuccess={() => {
          // Check if we need to return to a specific screen (like booking flow)
          if (route.params?.returnTo && route.params?.fromBooking) {
            // After successful verification, navigate to login with return parameters
            Alert.alert(
              'Registration Successful',
              'Your account has been verified. Please log in to complete your booking.',
              [
                {
                  text: 'OK',
                  onPress: () => navigation.navigate('Login', {
                    returnTo: route.params.returnTo,
                    returnParams: route.params.returnParams,
                    fromBooking: route.params.fromBooking
                  })
                }
              ]
            )
          } else {
            // Standard flow - just go to login
            Alert.alert(
              'Registration Successful',
              'Your account has been verified. Please log in.',
              [
                {
                  text: 'OK',
                  onPress: () => navigation.navigate('Login' as never)
                }
              ]
            )
          }
        }}
      />
    </View>
  )
}

// Create styles using the theme
const getStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
  },
  contentContainer: {
    padding: theme.spacing.lg,
  },
  header: {
    flexDirection: "row",
    marginBottom: theme.spacing.lg,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  title: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.md,
    marginBottom: theme.spacing.xl,
  },
  form: {
    marginTop: theme.spacing.md,
  },
  inputContainer: {
    marginBottom: theme.spacing.md,
  },
  label: {
    color: theme.colors.text,
    fontSize: theme.fontSizes.sm,
    fontWeight: "500",
    marginBottom: theme.spacing.xs,
  },
  inputWrapper: {
    alignItems: "center",
    backgroundColor: theme.colors.card,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    flexDirection: "row",
    paddingHorizontal: theme.spacing.sm,
  },
  inputIcon: {
    marginRight: theme.spacing.sm,
  },
  input: {
    color: theme.colors.text,
    flex: 1,
    fontSize: theme.fontSizes.md,
    paddingVertical: theme.spacing.md,
  },
  passwordToggle: {
    padding: theme.spacing.sm,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: theme.fontSizes.xs,
    marginTop: 4,
    marginLeft: 2,
  },
  helperText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.xs,
    marginTop: theme.spacing.xs,
    fontStyle: "italic",
  },
  passwordStrengthContainer: {
    marginTop: 8,
    marginBottom: 4,
  },
  strengthBarContainer: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    marginBottom: 4,
    overflow: 'hidden',
  },
  strengthBar: {
    height: '100%',
    width: '0%',
    borderRadius: 2,
  },
  strengthBarWeak: {
    backgroundColor: theme.colors.error,
    width: '33%',
  },
  strengthBarMedium: {
    backgroundColor: theme.colors.warning,
    width: '66%',
  },
  strengthBarStrong: {
    backgroundColor: theme.colors.success,
    width: '100%',
  },
  strengthText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.textLight,
  },
  termsContainer: {
    alignItems: "flex-start",
    flexDirection: "row",
    marginBottom: theme.spacing.lg,
    marginTop: theme.spacing.sm,
  },
  checkboxContainer: {
    marginRight: theme.spacing.sm,
    marginTop: 2, // Align with text
  },
  checkbox: {
    alignItems: "center",
    borderColor: theme.colors.border,
    borderRadius: 4,
    borderWidth: 2,
    height: 20,
    justifyContent: "center",
    width: 20,
  },
  checkboxChecked: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  termsText: {
    color: theme.colors.textLight,
    flex: 1,
    fontSize: theme.fontSizes.sm,
    lineHeight: 20,
  },
  termsLink: {
    color: theme.colors.primary,
    fontWeight: "500",
  },
  loginContainer: {
    alignItems: "center",
    marginTop: theme.spacing.lg,
  },
  loginText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
  },
  loginLink: {
    color: theme.colors.primary,
    fontWeight: "500",
  },
  applyContainer: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  applyText: {
    color: theme.colors.textLight,
    fontSize: theme.fontSizes.sm,
    marginRight: theme.spacing.sm,
  },
  applyButton: {
    backgroundColor: theme.colors.accent,
    borderRadius: theme.borderRadius.sm,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
  },
  applyButtonText: {
    color: theme.colors.card,
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
  },
})

export default RegisterScreen
