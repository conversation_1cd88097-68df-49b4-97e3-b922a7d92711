import { useState, useEffect } from "react"
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, Alert } from "react-native"
import { useNavigation, useRoute, type RouteProp } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import type { RootStackParamList } from "../navigation/RootNavigator"
import { getBookingById } from "../data/bookings"
import Header from "../components/Header"
import Card from "../components/Card"
import Button from "../components/Button"

type BookingDetailsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>
type BookingDetailsScreenRouteProp = RouteProp<RootStackParamList, "BookingDetails">

const BookingDetailsScreen = () => {
  const navigation = useNavigation<BookingDetailsScreenNavigationProp>()
  const route = useRoute<BookingDetailsScreenRouteProp>()
  const theme = useTheme()

  const { bookingId } = route.params || {}
  const [booking, setBooking] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (bookingId) {
      const bookingData = getBookingById(bookingId)
      setBooking(bookingData)
    }
    setLoading(false)
  }, [bookingId])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return theme.colors.success
      case "upcoming":
        return theme.colors.primary
      case "in-progress":
        return theme.colors.warning
      case "cancelled":
        return theme.colors.error
      default:
        return theme.colors.textLight
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return "Completed"
      case "upcoming":
        return "Upcoming"
      case "in-progress":
        return "In Progress"
      case "cancelled":
        return "Cancelled"
      default:
        return status
    }
  }

  const handleRebookService = () => {
    if (booking) {
      navigation.navigate("Booking", { serviceId: booking.serviceId })
    }
  }

  const handleCancelBooking = () => {
    Alert.alert(
      "Cancel Booking",
      "Are you sure you want to cancel this booking?",
      [
        {
          text: "No",
          style: "cancel"
        },
        {
          text: "Yes, Cancel",
          style: "destructive",
          onPress: () => {
            Alert.alert("Booking Cancelled", "Your booking has been cancelled successfully.")
            navigation.goBack()
          }
        }
      ]
    )
  }

  const handleContactProvider = () => {
    if (booking?.provider) {
      Alert.alert(
        "Contact Provider",
        "How would you like to contact the provider?",
        [
          {
            text: "Cancel",
            style: "cancel"
          },
          {
            text: "Chat",
            onPress: () => navigation.navigate("Chat", { providerId: booking.provider.id })
          }
        ]
      )
    }
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <Header title="Booking Details" showBackButton />
        <View style={styles.loadingContainer}>
          <Text>Loading booking details...</Text>
        </View>
      </View>
    )
  }

  if (!booking) {
    return (
      <View style={styles.container}>
        <Header title="Booking Details" showBackButton />
        <View style={styles.loadingContainer}>
          <Text>Booking not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            style={{ marginTop: theme.spacing.md }}
          />
        </View>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <Header title="Booking Details" showBackButton />
      <ScrollView style={styles.scrollView}>
        {/* Status Banner */}
        <View style={[styles.statusBanner, { backgroundColor: `${getStatusColor(booking.status)}20` }]}>
          <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(booking.status) }]} />
          <Text style={styles.statusText}>{getStatusLabel(booking.status)}</Text>
        </View>

        {/* Service Details */}
        <Card style={styles.card}>
          <Text style={styles.cardTitle}>Service Details</Text>
          <View style={styles.serviceRow}>
            <Image source={{ uri: booking.serviceImage }} style={styles.serviceImage} />
            <View style={styles.serviceInfo}>
              <Text style={styles.serviceName}>{booking.serviceName}</Text>
              <View style={styles.serviceDetail}>
                <Feather name="calendar" size={16} color={theme.colors.textLight} />
                <Text style={styles.serviceDetailText}>{booking.date}</Text>
              </View>
              <View style={styles.serviceDetail}>
                <Feather name="clock" size={16} color={theme.colors.textLight} />
                <Text style={styles.serviceDetailText}>{booking.time} ({booking.duration})</Text>
              </View>
            </View>
          </View>
        </Card>

        {/* Location Details */}
        <Card style={styles.card}>
          <Text style={styles.cardTitle}>Location</Text>
          <View style={styles.locationRow}>
            <View style={styles.locationIconContainer}>
              <Feather name="map-pin" size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.locationInfo}>
              <Text style={styles.locationLabel}>{booking.address.label}</Text>
              <Text style={styles.locationAddress}>{booking.address.address}</Text>
            </View>
          </View>
        </Card>

        {/* Provider Details */}
        {booking.provider && (
          <Card style={styles.card}>
            <Text style={styles.cardTitle}>Service Provider</Text>
            <View style={styles.providerRow}>
              <Image source={{ uri: booking.provider.image }} style={styles.providerImage} />
              <View style={styles.providerInfo}>
                <Text style={styles.providerName}>{booking.provider.name}</Text>
                <View style={styles.ratingContainer}>
                  <Feather name="star" size={16} color={theme.colors.warning} />
                  <Text style={styles.ratingText}>{booking.provider.rating}</Text>
                </View>
                <TouchableOpacity
                  style={styles.contactButton}
                  onPress={handleContactProvider}
                >
                  <Feather name="message-circle" size={16} color={theme.colors.primary} />
                  <Text style={styles.contactButtonText}>Contact</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Card>
        )}

        {/* Payment Details */}
        <Card style={styles.card}>
          <Text style={styles.cardTitle}>Payment Details</Text>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Payment Method</Text>
            <Text style={styles.paymentValue}>{booking.paymentMethod}</Text>
          </View>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Payment Status</Text>
            <Text
              style={[
                styles.paymentValue,
                {
                  color: booking.paymentStatus === "paid"
                    ? theme.colors.success
                    : booking.paymentStatus === "refunded"
                      ? theme.colors.error
                      : theme.colors.textDark
                }
              ]}
            >
              {booking.paymentStatus.charAt(0).toUpperCase() + booking.paymentStatus.slice(1)}
            </Text>
          </View>
          <View style={styles.divider} />
          <View style={styles.paymentRow}>
            <Text style={styles.totalLabel}>Total Amount</Text>
            <Text style={styles.totalValue}>D{booking.price}</Text>
          </View>
        </Card>

        {/* Notes */}
        {booking.notes && (
          <Card style={styles.card}>
            <Text style={styles.cardTitle}>Notes</Text>
            <Text style={styles.notesText}>{booking.notes}</Text>
          </Card>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {booking.status === "upcoming" && (
            <Button
              title="Cancel Booking"
              variant="outline"
              onPress={handleCancelBooking}
              style={{ marginBottom: theme.spacing.md }}
            />
          )}
          {booking.status === "completed" && (
            <Button
              title="Write a Review"
              variant="outline"
              onPress={() => {
                // Navigate to review screen or show review modal
                Alert.alert("Review", "Review functionality will be implemented soon.")
              }}
              style={{ marginBottom: theme.spacing.md }}
            />
          )}
          <Button
            title="Book Again"
            variant="primary"
            onPress={handleRebookService}
          />
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F7FA",
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  statusBanner: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: "600",
  },
  card: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  serviceRow: {
    flexDirection: "row",
  },
  serviceImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  serviceInfo: {
    flex: 1,
    justifyContent: "center",
  },
  serviceName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  serviceDetail: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  serviceDetailText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 8,
  },
  locationRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  locationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#E6F7FF",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 14,
    color: "#666",
  },
  providerRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  providerImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  ratingText: {
    fontSize: 14,
    marginLeft: 4,
  },
  contactButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#E6F7FF",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    alignSelf: "flex-start",
  },
  contactButtonText: {
    fontSize: 14,
    color: "#0088CC",
    marginLeft: 4,
  },
  paymentRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  paymentLabel: {
    fontSize: 14,
    color: "#666",
  },
  paymentValue: {
    fontSize: 14,
    fontWeight: "500",
  },
  divider: {
    height: 1,
    backgroundColor: "#E5E5E5",
    marginVertical: 12,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: "600",
  },
  totalValue: {
    fontSize: 16,
    fontWeight: "700",
  },
  notesText: {
    fontSize: 14,
    color: "#666",
  },
  actionButtons: {
    marginTop: 8,
    marginBottom: 24,
  },
})

export default BookingDetailsScreen
