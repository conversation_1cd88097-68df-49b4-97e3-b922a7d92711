import { useState, useEffect, useCallback } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from "react-native"
import { useNavigation, useFocusEffect } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather } from "@expo/vector-icons"
import * as ImagePicker from 'expo-image-picker'
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import { profileService, addressService } from "../api/services"
import { CustomerProfile, ProviderProfile } from "../types/user"
import type { RootStackParamList } from "../navigation/RootNavigator"
import Header from "../components/Header"
import Button from "../components/Button"
import AuthModal from "../components/AuthModal"
import { StatusBar } from "expo-status-bar"
import * as authStorage from "../utils/authStorage"

type ProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>
type ProfileType = CustomerProfile | ProviderProfile | null;

const ProfileScreen = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>()
  const theme = useTheme()
  const { logout, isAuthenticated, user, isAuthModalVisible, showAuthModal, hideAuthModal } = useAuth()

  const [profile, setProfile] = useState<ProfileType>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isImageUploading, setIsImageUploading] = useState(false)
  const [isOffline, setIsOffline] = useState(false)
  const [notifications, setNotifications] = useState({
    email: true,
    push: true,
    sms: false,
  })

  // Load profile data
  const loadProfile = async (showFullLoading = true) => {
    try {
      if (showFullLoading) {
        setIsLoading(true)
      }
      setIsOffline(false)

      // Get profile data
      const profileData = await profileService.getProfile()
      setProfile(profileData)

      // Cache the profile data for offline use
      await authStorage.saveProfileData(profileData)

      // Get address data if needed
      try {
        await addressService.getAddresses()
      } catch (addressError: unknown) {
        console.error('Error loading addresses:', addressError)

        // If it's a rate limit error, we can show a less alarming message
        if (typeof addressError === 'object' && addressError !== null && 'isRateLimited' in addressError) {
          console.log('Rate limited when loading addresses, will try again later')
          // We could set a flag to retry after some time if needed
        }
        // Non-critical error, don't show alert to user
      }
    } catch (error) {
      console.error('Error loading profile:', error)

      // Try to load profile from cache if available
      try {
        const cachedProfile = await authStorage.getProfileData()
        if (cachedProfile) {
          setProfile(cachedProfile)
          console.log('Loaded profile from cache')

          // Show a less alarming message for network errors
          if (error instanceof Error && error.message.includes('Network')) {
            setIsOffline(true)
            // Don't show alert for network errors, just use cached data silently
          } else {
            // Only show alert if it's not a refresh
            if (showFullLoading) {
              Alert.alert('Error', 'Failed to load profile data. Using cached data.')
            }
          }
        } else {
          // If no cached profile data, try to use the user data from auth context
          if (user) {
            // Create a basic profile from user data
            const basicProfile = {
              id: user.id,
              firstName: user.name?.split(' ')[0] || 'User',
              lastName: user.name?.split(' ').slice(1).join(' ') || '',
              email: user.email,
              role: user.role,
              // Add other required fields with default values
              phone: '',
              profileImage: null,
              addresses: [],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }
            setProfile(basicProfile)
            console.log('Created basic profile from user data')
          } else {
            // Only show alert if it's not a refresh
            if (showFullLoading) {
              Alert.alert('Error', 'Failed to load profile data. Please try again.')
            }
          }
        }
      } catch (cacheError) {
        console.error('Error loading cached profile:', cacheError)
        // Only show alert if it's not a refresh
        if (showFullLoading) {
          Alert.alert('Error', 'Failed to load profile data. Please try again.')
        }
      }
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  // Load profile when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadProfile()
    }
  }, [isAuthenticated, user])

  // Refresh profile when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (isAuthenticated && user && !isLoading) {
        // Use a lighter refresh that doesn't show the full loading screen
        loadProfile(false)
      }
    }, [isAuthenticated, user])
  )

  // Handle pull-to-refresh
  const handleRefresh = () => {
    if (isAuthenticated && user) {
      setIsRefreshing(true)
      loadProfile(false)
    }
  }

  // Handle profile image upload
  const handleProfileImageUpload = async () => {
    try {
      // Request permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()

      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'You need to grant permission to access your photos to upload a profile image.')
        return
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      })

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0]

        // Show loading indicator
        setIsImageUploading(true)

        try {
          // Create form data for image upload
          const formData = new FormData()
          formData.append('profileImage', {
            uri: selectedImage.uri,
            type: 'image/jpeg',
            name: 'profile-image.jpg',
          } as unknown as Blob)

          // Update profile with new image
          const updatedProfile = await profileService.updateProfile({
            profileImage: selectedImage.uri,
          })

          // Update local state
          setProfile(updatedProfile)

          // Update cache
          await authStorage.saveProfileData(updatedProfile)

          // Show success message
          Alert.alert('Success', 'Profile image updated successfully.')
        } catch (error) {
          console.error('Error uploading profile image:', error)
          Alert.alert('Error', 'Failed to upload profile image. Please try again.')
        } finally {
          setIsImageUploading(false)
        }
      }
    } catch (error) {
      console.error('Error picking image:', error)
      Alert.alert('Error', 'Failed to select image. Please try again.')
      setIsImageUploading(false)
    }
  }

  // Handle logout
  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout()
              // Navigation is handled by the AuthContext
            } catch (error) {
              console.error('Logout error:', error)
              Alert.alert('Error', 'Failed to logout. Please try again.')
            }
          },
        },
      ]
    )
  }

  const styles = StyleSheet.create({
    // Layout styles
    container: {
      backgroundColor: theme.colors.background,
      flex: 1,
    },
    content: {
      padding: theme.spacing.md,
    },

    // Loading state styles
    loadingContainer: {
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center',
    },
    loadingText: {
      color: theme.colors.text,
      fontSize: theme.fontSizes.md,
      marginTop: theme.spacing.md,
    },
    signInText: {
      color: theme.colors.text,
      fontSize: theme.fontSizes.md,
      marginTop: 16,
      textAlign: 'center',
    },
    signInButton: {
      marginTop: 16,
    },

    // Profile header styles
    profileHeader: {
      backgroundColor: theme.colors.primary,
      borderBottomLeftRadius: 24,
      borderBottomRightRadius: 24,
      elevation: 4,
      marginBottom: theme.spacing.md,
      paddingBottom: theme.spacing.lg,
      paddingHorizontal: theme.spacing.lg,
      paddingTop: theme.spacing.xl,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    headerContent: {
      alignItems: 'center',
      flexDirection: 'column',
      justifyContent: 'center',
      width: '100%',
    },
    avatarContainer: {
      alignItems: 'center',
      marginBottom: theme.spacing.md,
      marginTop: '2%', // Add a slight top margin for better spacing
      position: 'relative',
    },
    avatar: {
      borderColor: theme.colors.background,
      borderRadius: 60,
      borderWidth: 4,
      elevation: 5,
      height: 120,
      // Add shadow to the avatar for a more professional look
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 5,
      width: 120,
    },
    editAvatarButton: {
      alignItems: 'center',
      backgroundColor: theme.colors.accent,
      borderColor: theme.colors.background,
      borderRadius: 18,
      borderWidth: 1.5,
      bottom: 0,
      elevation: 4,
      height: 36,
      justifyContent: 'center',
      position: 'absolute',
      right: 0,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
      width: 36,
    },
    profileInfo: {
      alignItems: 'center',
      marginTop: 16,
      paddingHorizontal: theme.spacing.md,
      width: '100%',
    },
    name: {
      color: theme.colors.background,
      fontSize: theme.fontSizes.xl,
      fontWeight: "700",
      letterSpacing: 0.5,
      marginBottom: 6,
      textAlign: 'center',
      // textShadowColor: theme.colors.text,
      // textShadowOffset: { width: 0, height: 1 },
      // textShadowRadius: 2,
    },
    email: {
      color: theme.colors.background,
      fontSize: theme.fontSizes.sm,
      letterSpacing: 0.3,
      marginBottom: theme.spacing.sm,
      opacity: 0.9,
      textAlign: 'center',
    },
    roleContainer: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: 8,
    },
    role: {
      backgroundColor: theme.colors.background,
      borderRadius: 16,
      color: theme.colors.primary,
      elevation: 2,
      fontSize: theme.fontSizes.xs,
      fontWeight: '600',
      letterSpacing: 0.5,
      paddingHorizontal: 16,
      paddingVertical: 6,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 1,
    },

    // Section styles
    sectionTitle: {
      color: theme.colors.textLight,
      fontSize: theme.fontSizes.sm,
      fontWeight: "600",
      letterSpacing: 0.5,
      marginBottom: theme.spacing.sm,
      marginTop: theme.spacing.md,
      paddingHorizontal: 4,
      textTransform: 'uppercase',
    },

    // Card styles
    card: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      elevation: 2,
      marginBottom: theme.spacing.md,
      overflow: 'hidden',
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
    },

    // Menu item styles
    menuItem: {
      alignItems: "center",
      flexDirection: "row",
      padding: theme.spacing.md,
    },
    menuItemWithBorder: {
      borderBottomColor: theme.colors.border,
      borderBottomWidth: 1,
    },
    menuIconContainer: {
      alignItems: "center",
      backgroundColor: `${theme.colors.accent}20`,
      borderRadius: 12,
      height: 40,
      justifyContent: "center",
      marginRight: theme.spacing.md,
      width: 40,
    },
    menuText: {
      flex: 1,
      fontSize: theme.fontSizes.md,
    },
    menuValue: {
      color: theme.colors.textLight,
      fontSize: theme.fontSizes.sm,
      marginRight: theme.spacing.sm,
    },
    menuArrow: {
      color: theme.colors.textLight,
    },

    // Notification item styles
    notificationItem: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      padding: theme.spacing.md,
    },
    notificationInfo: {
      flex: 1,
      marginLeft: theme.spacing.md,
    },
    notificationTitle: {
      fontSize: theme.fontSizes.md,
      fontWeight: "500",
    },
    notificationDescription: {
      color: theme.colors.textLight,
      fontSize: theme.fontSizes.sm,
    },

    // Logout button styles
    logoutButton: {
      alignItems: "center",
      flexDirection: "row",
      padding: theme.spacing.md,
    },
    logoutText: {
      color: theme.colors.notification,
      flex: 1,
      fontSize: theme.fontSizes.md,
    },

    // Version info styles
    versionContainer: {
      alignItems: "center",
      marginBottom: theme.spacing.xl,
      marginTop: theme.spacing.lg,
    },
    versionText: {
      color: theme.colors.textLight,
      fontSize: theme.fontSizes.xs,
    },
    copyrightText: {
      color: theme.colors.textLight,
      fontSize: theme.fontSizes.xs,
      marginTop: 4,
    },

    // Offline indicator styles
    offlineBar: {
      alignItems: 'center',
      backgroundColor: theme.colors.notification,
      padding: theme.spacing.sm,
    },
    offlineText: {
      color: theme.colors.background,
      fontSize: theme.fontSizes.sm,
      fontWeight: '500',
    },


  })

  // Show auth modal when user is not authenticated
  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      // Show the auth modal when the user is not authenticated
      showAuthModal();
    }
  }, [isAuthenticated, isLoading, showAuthModal]);

  // Show loading indicator while fetching data
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    )
  }

  // Show a placeholder screen if not authenticated
  if (!isAuthenticated) {
    return (
      <View style={styles.container}>
        <Header title="Profile & Settings" showBackButton />
        <View style={styles.loadingContainer}>
          <Feather name="user-x" size={60} color={theme.colors.textLight} />
          <Text style={styles.signInText}>
            Please sign in to view your profile
          </Text>
          <Button
            title="Sign In"
            variant="primary"
            onPress={showAuthModal}
            style={styles.signInButton}
          />
        </View>

        {/* Auth Modal */}
        <AuthModal
          visible={isAuthModalVisible}
          onClose={hideAuthModal}
          returnTo="Profile"
          message="Sign in to access your profile and account settings"
        />
      </View>
    );
  }

  // Get user role display name
  const getRoleDisplayName = () => {
    if (!profile) return '';

    switch (profile.role) {
      case 'PROVIDER':
        return 'Service Provider';
      case 'CUSTOMER':
        return 'Customer';
      case 'ADMIN':
        return 'Administrator';
      default:
        return profile.role;
    }
  };

  // Format phone number for display
  const formatPhoneNumber = (phone?: string) => {
    if (!phone) return 'Not provided';

    // Simple formatting for display
    if (phone.length === 10) {
      return `(${phone.substring(0, 3)}) ${phone.substring(3, 6)}-${phone.substring(6)}`;
    }

    return phone;
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Header with profile info */}
      <View style={styles.profileHeader}>
        <View style={styles.headerContent}>
          <View style={styles.avatarContainer}>
            <Image
              source={profile?.profileImage
                ? { uri: profile.profileImage }
                : { uri: "https://randomuser.me/api/portraits/lego/1.jpg" }}
              style={styles.avatar}
            />
            <TouchableOpacity
              style={styles.editAvatarButton}
              onPress={handleProfileImageUpload}
              accessibilityLabel="Edit profile picture"
            >
              <Feather name="edit-2" size={14} color={theme.colors.background} />
            </TouchableOpacity>
          </View>

          <View style={styles.profileInfo}>
            <Text style={styles.name}>
              {profile?.firstName || 'User'} {profile?.lastName || ''}
            </Text>
            <Text style={styles.email}>{profile?.email || 'No email provided'}</Text>
            <View style={styles.roleContainer}>
              <Text style={styles.role}>{getRoleDisplayName()}</Text>
            </View>
          </View>
        </View>
      </View>

      {isOffline && (
        <View style={styles.offlineBar}>
          <Text style={styles.offlineText}>You are offline. Using cached data.</Text>
        </View>
      )}

      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.content}>
          {/* Personal Information */}
          <Text style={styles.sectionTitle}>Personal Information</Text>
          <View style={styles.card}>
            <TouchableOpacity style={[styles.menuItem, styles.menuItemWithBorder]}>
              <View style={styles.menuIconContainer}>
                <Feather name="user" size={20} color={theme.colors.accent} />
              </View>
              <Text style={styles.menuText}>Full Name</Text>
              <Text style={styles.menuValue}>
                {profile?.firstName} {profile?.lastName}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.menuItem, styles.menuItemWithBorder]}>
              <View style={styles.menuIconContainer}>
                <Feather name="mail" size={20} color={theme.colors.accent} />
              </View>
              <Text style={styles.menuText}>Email Address</Text>
              <Text style={styles.menuValue}>{profile?.email}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem}>
              <View style={styles.menuIconContainer}>
                <Feather name="phone" size={20} color={theme.colors.accent} />
              </View>
              <Text style={styles.menuText}>Phone Number</Text>
              <Text style={styles.menuValue}>{formatPhoneNumber(profile?.phone)}</Text>
            </TouchableOpacity>
          </View>

          {/* Account Settings */}
          <Text style={styles.sectionTitle}>Account Settings</Text>
          <View style={styles.card}>
            <TouchableOpacity
              style={[styles.menuItem, styles.menuItemWithBorder]}
              onPress={() => Alert.alert('Coming Soon', 'Edit profile functionality will be available in a future update.')}
            >
              <View style={styles.menuIconContainer}>
                <Feather name="edit" size={20} color={theme.colors.accent} />
              </View>
              <Text style={styles.menuText}>Edit Profile</Text>
              <Feather name="chevron-right" size={20} style={styles.menuArrow} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.menuItem, styles.menuItemWithBorder]}
              onPress={() => Alert.alert('Coming Soon', 'Address management will be available in a future update.')}
            >
              <View style={styles.menuIconContainer}>
                <Feather name="home" size={20} color={theme.colors.accent} />
              </View>
              <Text style={styles.menuText}>Saved Addresses</Text>
              <Feather name="chevron-right" size={20} style={styles.menuArrow} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => navigation.navigate("PaymentMethods")}
            >
              <View style={styles.menuIconContainer}>
                <Feather name="credit-card" size={20} color={theme.colors.accent} />
              </View>
              <Text style={styles.menuText}>Payment Methods</Text>
              <Feather name="chevron-right" size={20} style={styles.menuArrow} />
            </TouchableOpacity>
          </View>

          {/* Notification Settings */}
          <Text style={styles.sectionTitle}>Notification Preferences</Text>
          <View style={styles.card}>
            <View style={[styles.notificationItem, styles.menuItemWithBorder]}>
              <View style={styles.menuIconContainer}>
                <Feather name="mail" size={20} color={theme.colors.accent} />
              </View>
              <View style={styles.notificationInfo}>
                <Text style={styles.notificationTitle}>Email Notifications</Text>
                <Text style={styles.notificationDescription}>Receive booking updates via email</Text>
              </View>
              <Switch
                value={notifications.email}
                onValueChange={(value) => setNotifications({ ...notifications, email: value })}
                trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                thumbColor={theme.colors.background}
                ios_backgroundColor={theme.colors.border}
              />
            </View>

            <View style={[styles.notificationItem, styles.menuItemWithBorder]}>
              <View style={styles.menuIconContainer}>
                <Feather name="bell" size={20} color={theme.colors.accent} />
              </View>
              <View style={styles.notificationInfo}>
                <Text style={styles.notificationTitle}>Push Notifications</Text>
                <Text style={styles.notificationDescription}>Receive real-time updates on your device</Text>
              </View>
              <Switch
                value={notifications.push}
                onValueChange={(value) => setNotifications({ ...notifications, push: value })}
                trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                thumbColor={theme.colors.background}
                ios_backgroundColor={theme.colors.border}
              />
            </View>

            <View style={styles.notificationItem}>
              <View style={styles.menuIconContainer}>
                <Feather name="message-square" size={20} color={theme.colors.accent} />
              </View>
              <View style={styles.notificationInfo}>
                <Text style={styles.notificationTitle}>SMS Notifications</Text>
                <Text style={styles.notificationDescription}>Receive text messages for important updates</Text>
              </View>
              <Switch
                value={notifications.sms}
                onValueChange={(value) => setNotifications({ ...notifications, sms: value })}
                trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
                thumbColor={theme.colors.background}
                ios_backgroundColor={theme.colors.border}
              />
            </View>
          </View>

          {/* Support & Other Settings */}
          <Text style={styles.sectionTitle}>Support & More</Text>
          <View style={styles.card}>
            <TouchableOpacity
              style={[styles.menuItem, styles.menuItemWithBorder]}
              onPress={() => Alert.alert('Coming Soon', 'Help & Support center will be available in a future update.')}
            >
              <View style={styles.menuIconContainer}>
                <Feather name="help-circle" size={20} color={theme.colors.accent} />
              </View>
              <Text style={styles.menuText}>Help & Support</Text>
              <Feather name="chevron-right" size={20} style={styles.menuArrow} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.menuItem, styles.menuItemWithBorder]}
              onPress={() => Alert.alert('Coming Soon', 'App settings will be available in a future update.')}
            >
              <View style={styles.menuIconContainer}>
                <Feather name="settings" size={20} color={theme.colors.accent} />
              </View>
              <Text style={styles.menuText}>App Settings</Text>
              <Feather name="chevron-right" size={20} style={styles.menuArrow} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.logoutButton}
              onPress={handleLogout}
            >
              <View style={[styles.menuIconContainer, { backgroundColor: `${theme.colors.notification}20` }]}>
                <Feather name="log-out" size={20} color={theme.colors.notification} />
              </View>
              <Text style={styles.logoutText}>Log Out</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.versionContainer}>
            <Text style={styles.versionText}>CleanConnect v1.0.0</Text>
            <Text style={styles.copyrightText}>© {new Date().getFullYear()} CleanConnect. All rights reserved.</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  )
}

export default ProfileScreen
