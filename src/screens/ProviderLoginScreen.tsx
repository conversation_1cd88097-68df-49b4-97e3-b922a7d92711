import { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Dimensions,
  Alert,
  SafeAreaView,
  StatusBar,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Animated,
  Vibration,
} from "react-native";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Feather } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import { useAuth } from "../context/AuthContext";
import type { RootStackParamList } from "../navigation/RootNavigator";
import Button from "../components/Button";
import OtpVerificationModal from "../components/OtpVerificationModal";
import { ToastService } from "../components/ToastManager";
import Storage from "../utils/storage";
import authService from "../api/services/authService";
import * as SecureStore from 'expo-secure-store';

type ProviderLoginScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;
type ProviderLoginScreenRouteProp = RouteProp<RootStackParamList, 'ProviderLogin'>;

const ProviderLoginScreen = () => {
  const navigation = useNavigation<ProviderLoginScreenNavigationProp>();
  const route = useRoute<ProviderLoginScreenRouteProp>();
  const theme = useTheme();
  const { setUser, setIsAuthenticated } = useAuth();

  // Form state
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Form validation
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [formTouched, setFormTouched] = useState(false);
  const [loginError, setLoginError] = useState("");

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [otpEmail, setOtpEmail] = useState("");
  const [otpPurpose, setOtpPurpose] = useState<'REGISTER' | 'LOGIN' | 'RESET'>('LOGIN');

  // Animation values
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  // Refs
  const passwordInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);

  // Get screen dimensions for responsive layout
  const { width } = Dimensions.get("window");

  // Load remembered credentials
  useEffect(() => {
    const loadRememberedCredentials = async () => {
      try {
        const rememberedEmail = await SecureStore.getItemAsync('cached_email');
        const rememberMeEnabled = await SecureStore.getItemAsync('remember_me');

        if (rememberedEmail && rememberMeEnabled === 'true') {
          setEmail(rememberedEmail);
          setRememberMe(true);
        }

        // Pre-fill with cleaner credentials for testing
        if (__DEV__) {
          setEmail('<EMAIL>');
          setPassword('M12345a@');
          console.log('Pre-filled cleaner credentials for provider login');
        }
      } catch (error) {
        console.error('Error loading remembered credentials:', error);
      }
    };

    loadRememberedCredentials();
  }, []);

  // Shake animation for form errors
  const startShakeAnimation = () => {
    Vibration.vibrate(200);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();
  };

  // Validate email
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError("Email is required");
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email address");
      return false;
    } else {
      setEmailError("");
      return true;
    }
  };

  // Validate password
  const validatePassword = (password: string): boolean => {
    if (!password) {
      setPasswordError("Password is required");
      return false;
    } else if (password.length < 6) {
      setPasswordError("Password must be at least 6 characters");
      return false;
    } else {
      setPasswordError("");
      return true;
    }
  };

  // Handle login
  const handleLogin = async () => {
    // Set form as touched to show validation errors
    setFormTouched(true);

    // Clear any previous login errors
    setLoginError("");

    // Validate form
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);

    if (!isEmailValid || !isPasswordValid) {
      // Don't proceed if validation fails
      startShakeAnimation();
      return;
    }

    try {
      setIsLoading(true);
      
      // Prepare login data for provider
      const loginData = {
        email: email.trim(),
        password: password,
        role: 'PROVIDER',
        rememberMe
      };

      // Dismiss keyboard
      Keyboard.dismiss();

      // Call login API through auth service
      const response = await authService.login(loginData);

      console.log('Login response:', response);

      if (!response.success) {
        // Show specific error message
        const errorMessage = response.error || "Please check your credentials and try again.";
        setLoginError(errorMessage);
        startShakeAnimation();
        setIsLoading(false);
        return;
      }

      // Check if email verification is required
      if (response.data?.requiresVerification) {
        // Show OTP verification modal
        setOtpEmail(email);
        setOtpPurpose('LOGIN');
        setShowOtpModal(true);
        setIsLoading(false);
        return;
      }

      // If we have a response with a user, login was successful
      if (response.user || response.data?.user) {
        console.log('Provider login successful!');
        const userData = response.user || response.data?.user;

        // Store remember me preference
        if (rememberMe) {
          await Storage.setItem('cached_email', email.trim());
          await SecureStore.setItemAsync('cached_email', email.trim());
          await SecureStore.setItemAsync('remember_me', 'true');
        } else {
          // Clear remembered credentials if remember me is not checked
          await SecureStore.deleteItemAsync('remember_me');
          await SecureStore.deleteItemAsync('cached_email');
        }

        // Set the user and authentication state in the AuthContext
        setUser(userData);
        setIsAuthenticated(true);

        // Navigate to provider dashboard
        navigation.reset({
          index: 0,
          routes: [{ name: 'Provider' }],
        });

        // Show success toast
        ToastService.show({
          type: 'success',
          text1: 'Login Successful',
          text2: 'Welcome back to CleanConnect!',
        });
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setLoginError(error.message || 'An unexpected error occurred. Please try again.');
      startShakeAnimation();
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP success
  const handleOtpSuccess = () => {
    // After OTP verification, try login again
    handleLogin();
  };

  // Handle forgot password
  const handleForgotPassword = () => {
    navigation.navigate("ForgotPassword");
  };

  // Handle back button
  const handleBack = () => {
    navigation.navigate("RoleSelection");
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar backgroundColor={theme.colors.background} barStyle="dark-content" />

      {/* Back Button */}
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Feather name="arrow-left" size={24} color={theme.colors.text} />
      </TouchableOpacity>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            <View style={styles.centeredContent}>
              <Image
                source={{ uri: "https://picsum.photos/id/24/200/200" }}
                style={styles.logo}
              />
              <Text style={[styles.title, { color: theme.colors.text }]}>
                Cleaner Login
              </Text>
              <Text style={[styles.subtitle, { color: theme.colors.textLight }]}>
                Sign in to your cleaner account
              </Text>
            </View>

            <Animated.View
              style={[
                styles.formContainer,
                { transform: [{ translateX: shakeAnimation }] }
              ]}
            >
              {loginError ? (
                <View style={[styles.errorContainer, { backgroundColor: `${theme.colors.error}20` }]}>
                  <Feather name="alert-circle" size={20} color={theme.colors.error} />
                  <Text style={[styles.errorText, { color: theme.colors.error }]}>
                    {loginError}
                  </Text>
                </View>
              ) : null}

              {/* Email Input */}
              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: theme.colors.text }]}>Email</Text>
                <View style={[styles.inputWrapper, {
                  backgroundColor: theme.colors.card,
                  borderColor: formTouched && emailError ? theme.colors.error : theme.colors.border,
                }]}>
                  <Feather name="mail" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                  <TextInput
                    ref={emailInputRef}
                    style={[styles.textInput, { color: theme.colors.text }]}
                    placeholder="Enter your email"
                    placeholderTextColor={theme.colors.textLight}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    value={email}
                    onChangeText={(text) => {
                      setEmail(text);
                      if (formTouched) validateEmail(text);
                      // Clear login error when user types
                      if (loginError) setLoginError("");
                    }}
                    onBlur={() => {
                      setFormTouched(true);
                      validateEmail(email);
                    }}
                    returnKeyType="next"
                    onSubmitEditing={() => passwordInputRef.current?.focus()}
                    accessibilityLabel="Email input field"
                  />
                </View>
                {formTouched && emailError ? (
                  <Text style={styles.errorText}>{emailError}</Text>
                ) : null}
              </View>

              {/* Password Input */}
              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: theme.colors.text }]}>Password</Text>
                <View style={[styles.inputWrapper, {
                  backgroundColor: theme.colors.card,
                  borderColor: formTouched && passwordError ? theme.colors.error : theme.colors.border,
                }]}>
                  <Feather name="lock" size={20} color={theme.colors.textLight} style={styles.inputIcon} />
                  <TextInput
                    ref={passwordInputRef}
                    style={[styles.textInput, { color: theme.colors.text }]}
                    placeholder="Enter your password"
                    placeholderTextColor={theme.colors.textLight}
                    secureTextEntry={!showPassword}
                    value={password}
                    onChangeText={(text) => {
                      setPassword(text);
                      if (formTouched) validatePassword(text);
                      // Clear login error when user types
                      if (loginError) setLoginError("");
                    }}
                    onBlur={() => {
                      setFormTouched(true);
                      validatePassword(password);
                    }}
                    returnKeyType="done"
                    onSubmitEditing={handleLogin}
                    accessibilityLabel="Password input field"
                  />
                  <TouchableOpacity
                    style={styles.passwordToggle}
                    onPress={() => setShowPassword(!showPassword)}
                    accessibilityLabel={showPassword ? "Hide password" : "Show password"}
                  >
                    <Feather
                      name={showPassword ? "eye-off" : "eye"}
                      size={20}
                      color={theme.colors.textLight}
                    />
                  </TouchableOpacity>
                </View>
                {formTouched && passwordError ? (
                  <Text style={styles.errorText}>{passwordError}</Text>
                ) : null}
              </View>

              {/* Remember Me Checkbox */}
              <View style={styles.checkboxContainer}>
                <TouchableOpacity
                  style={styles.checkboxWrapper}
                  onPress={() => setRememberMe(!rememberMe)}
                  accessibilityLabel="Remember me checkbox"
                  accessibilityRole="checkbox"
                  accessibilityState={{ checked: rememberMe }}
                >
                  <View
                    style={[
                      styles.checkbox,
                      {
                        backgroundColor: rememberMe ? theme.colors.primary : 'transparent',
                        borderColor: rememberMe ? theme.colors.primary : theme.colors.border,
                      },
                    ]}
                  >
                    {rememberMe && <Feather name="check" size={14} color="#FFFFFF" />}
                  </View>
                  <Text style={[styles.checkboxLabel, { color: theme.colors.text }]}>
                    Remember me
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.forgotPasswordContainer}
                  onPress={handleForgotPassword}
                  accessibilityLabel="Forgot password button"
                  accessibilityHint="Navigate to password reset screen"
                >
                  <Text style={[styles.forgotPasswordText, { color: theme.colors.primary }]}>
                    Forgot Password?
                  </Text>
                </TouchableOpacity>
              </View>

              <Button
                title={isLoading ? "Signing in..." : "Sign In"}
                variant="warning"
                onPress={handleLogin}
                fullWidth
                loading={isLoading}
                disabled={isLoading || (formTouched && (!!emailError || !!passwordError))}
                accessibilityLabel="Sign in button"
                accessibilityHint="Signs you into your account"
              />
            </Animated.View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* OTP Verification Modal */}
      <OtpVerificationModal
        visible={showOtpModal}
        onClose={() => setShowOtpModal(false)}
        email={otpEmail}
        purpose={otpPurpose}
        userRole="provider"
        onSuccess={handleOtpSuccess}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 10,
    padding: 8,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  centeredContent: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 32,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    color: '#FF3B30',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    height: 56,
    paddingHorizontal: 16,
  },
  inputIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  passwordToggle: {
    padding: 8,
  },
  checkboxContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  checkboxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  checkboxLabel: {
    fontSize: 14,
  },
  forgotPasswordContainer: {
    padding: 4,
  },
  forgotPasswordText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ProviderLoginScreen;
