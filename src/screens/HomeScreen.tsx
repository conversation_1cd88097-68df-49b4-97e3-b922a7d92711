import React, { useState, useRef, useEffect, useCallback } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Dimensions, FlatList, Animated, TextInput, ActivityIndicator, Alert, RefreshControl } from "react-native"
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { Feather, FontAwesome5 } from "@expo/vector-icons"
import { useTheme } from "../context/ThemeContext"
import { useAuth } from "../context/AuthContext"
import { useResponsive } from "../hooks/useResponsive"
import type { RootStackParamList } from "../navigation/RootNavigator"
// Import fallback data only for types and as fallback
import { quickBookingServices, topRatedCleaners } from "../data/services"
import { Service } from "../types/service"
import serviceService from "../api/services/serviceService"
import Card from "../components/Card"
import { useLocation } from "../context/LocationContext"
import LocationPermissionModal from "../components/LocationPermissionModal"
import * as Location from "expo-location"
import { STORAGE_KEYS } from "../config/constants"
import Storage from "../utils/storage"
import { customerService } from '../api/services'
import { Provider } from '../api/services/customerService'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>()
  const theme = useTheme()
  const { isAuthenticated: authStatus, setIsAuthenticated } = useAuth() // Use authStatus to avoid conflict with local state
  const { height } = useResponsive()
  const { width } = Dimensions.get('window')
  const insets = useSafeAreaInsets()

  // No longer need dynamic margin top as we'll use SafeAreaInsets
  const [loadingServiceId, setLoadingServiceId] = useState<string | null>(null)
  const [activePromotionIndex, setActivePromotionIndex] = useState(0)
  const promotionRef = useRef<FlatList>(null)
  const fadeAnim = useRef(new Animated.Value(1)).current
  const scaleAnim = useRef(new Animated.Value(1)).current

  // Search state and animation
  const [searchPlaceholderIndex, setSearchPlaceholderIndex] = useState(0)
  const [searchText, setSearchText] = useState("")
  const [isSearchFocused, setIsSearchFocused] = useState(false)
  const searchTextOpacity = useRef(new Animated.Value(1)).current

  // Location state
  const { currentLocation, isLoading: isLocationLoading, addLocation, savedLocations } = useLocation()

  // Location permission modal state
  const [showLocationPermissionModal, setShowLocationPermissionModal] = useState(false)

  // Services state
  const [services, setServices] = useState<Service[]>([])
  const [isServicesLoading, setIsServicesLoading] = useState(true)
  const [providers, setProviders] = useState<Provider[]>([])
  const [isProvidersLoading, setIsProvidersLoading] = useState(true)

  // Add new state variables
  const [refreshing, setRefreshing] = useState(false)
  const [servicesError, setServicesError] = useState<string | null>(null)
  const [providersError, setProvidersError] = useState<string | null>(null)

  // Fetch services from API with proper error handling and caching
  const fetchServices = useCallback(async (forceRefresh = false) => {
    try {
      setIsServicesLoading(true)
      setServicesError(null)

      // Fetch services from the API
      const servicesData = await serviceService.getServices()

      if (servicesData && servicesData.length > 0) {
        // Map the services to include the icon property needed for the UI
        const mappedServices = servicesData.map((service: Service) => {
          // Determine icon based on service name or use default
          let icon = "home"
          const serviceName = (service.name || service.title || "").toLowerCase()

          if (serviceName.includes("deep") || serviceName.includes("thorough")) {
            icon = "refresh-cw"
          } else if (serviceName.includes("move") || serviceName.includes("relocation")) {
            icon = "truck"
          } else if (serviceName.includes("window")) {
            icon = "square"
          } else if (serviceName.includes("garden") || serviceName.includes("lawn")) {
            icon = "scissors"
          } else if (serviceName.includes("office")) {
            icon = "briefcase"
          } else if (serviceName.includes("sanitization") || serviceName.includes("disinfect")) {
            icon = "shield"
          } else if (serviceName.includes("carpet")) {
            icon = "layers"
          }

          return {
            ...service,
            name: service.name || service.title || "Cleaning Service",
            icon
          }
        })

        setServices(mappedServices)
      }
    } catch (error) {
      console.error('Error fetching services:', error)
      setServicesError('Failed to load services. Please try again.')
      // If there's an error, use the fallback data from quickBookingServices
      const fallbackServices: Service[] = quickBookingServices.map(service => ({
        id: service.id,
        name: service.name,
        description: '',
        price: 0,
        isActive: true,
        icon: service.icon
      }))
      setServices(fallbackServices)
    } finally {
      setIsServicesLoading(false)
    }
  }, [])

  // Fetch providers from API with proper error handling and caching
  const fetchProviders = useCallback(async (forceRefresh = false) => {
    try {
      setIsProvidersLoading(true)
      setProvidersError(null)

      // Fetch top rated providers from the API
      const providersData = await customerService.getTopRatedProviders(5)

      if (providersData && providersData.length > 0) {
        setProviders(providersData)
      }
    } catch (error) {
      console.error('Error fetching providers:', error)
      setProvidersError('Failed to load providers. Please try again.')
      // Fallback to mock data is already set in the initial state
    } finally {
      setIsProvidersLoading(false)
    }
  }, [])

  // Handle pull to refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await Promise.all([
        fetchServices(true),
        fetchProviders(true)
      ])
    } catch (error) {
      console.error('Error refreshing data:', error)
    } finally {
      setRefreshing(false)
    }
  }, [fetchServices, fetchProviders])

  // Initial data fetch
  useEffect(() => {
    fetchServices()
    fetchProviders()
  }, [fetchServices, fetchProviders])

  // Search placeholder suggestions - only the changing part
  const searchKeywords = [
    "cleaning services",
    "professional providers",
    "deep cleaning",
    "regular maintenance",
    "same-day service",
    "eco-friendly options",
    "specialized cleaning"
  ]

  // Check authentication status and location permission on mount
  useEffect(() => {
    const checkAuthAndLocation = async () => {
      try {
        // Check if user is authenticated
        const isAuth = await Storage.getItem('isAuthenticated')
        setIsAuthenticated(isAuth === 'true')

        // Check if onboarding is completed
        const onboardingCompleted = await Storage.getItem('onboardingCompleted')

        // Check user role
        const userRole = await Storage.getItem('userRole')

        // Check location permission status
        const locationPermissionStatus = await Storage.getItem(STORAGE_KEYS.LOCATION_PERMISSION)

        console.log('Location permission status:', locationPermissionStatus)
        console.log('Saved locations:', savedLocations)

        // Don't show the location permission modal immediately after role selection
        // Instead, wait for the user to interact with the app first
        if (
          // Case 1: User completed onboarding but hasn't set location permission
          (onboardingCompleted === 'true' && userRole === 'customer' && (!locationPermissionStatus || locationPermissionStatus === 'later')) ||
          // Case 2: User is authenticated but has no saved locations
          (isAuth === 'true' && userRole === 'customer' && savedLocations.length === 0)
        ) {
          console.log('Location permission needed, but not showing modal immediately')
          // We'll show the modal when the user tries to book a service or after a delay
          // This makes the app more user-friendly

          // Show the modal after a 5-second delay to give the user time to explore the app
          setTimeout(() => {
            setShowLocationPermissionModal(true)
          }, 5000)
        }
      } catch (error) {
        console.error('Error checking auth and location:', error)
      }
    }

    checkAuthAndLocation()
  }, [savedLocations]) // Add savedLocations as a dependency

  // Handle location permission granted
  // Helper function to generate a friendly location name in neighborhood-city format
  const generateFriendlyLocationName = (address: Location.LocationGeocodedAddress): string => {
    // For the best format, try to combine district/neighborhood with city
    if (address.district && address.city) {
      return `${address.district}-${address.city}`
    }

    // If we have a subregion and city, use that combination
    if (address.subregion && address.city && !containsNumbers(address.subregion)) {
      return `${address.subregion}-${address.city}`
    }

    // If we have a named point of interest and city, combine them
    if (address.name && address.city && !containsNumbers(address.name)) {
      return `${address.name}-${address.city}`
    }

    // If we have just a district/neighborhood, use that
    if (address.district) {
      return address.district
    }

    // If we have a named point of interest, use that
    if (address.name && !containsNumbers(address.name)) {
      return address.name
    }

    // For urban areas, use the subregion name
    if (address.subregion && !containsNumbers(address.subregion)) {
      return address.subregion
    }

    // Use city name
    if (address.city) {
      return address.city
    }

    // Use region/state name
    if (address.region) {
      return address.region
    }

    // If we have a street name without numbers, use that
    if (address.street && !containsNumbers(address.street)) {
      // Extract just the street name without any numbers
      const streetNameOnly = extractStreetName(address.street)
      return streetNameOnly || address.street
    }

    // Fallback to country
    if (address.country) {
      return address.country
    }

    // Last resort fallback
    return "Current Location"
  }

  // Helper function to check if a string contains numbers
  const containsNumbers = (str: string): boolean => {
    return /\d/.test(str)
  }

  // Helper function to extract just the street name without numbers
  const extractStreetName = (street: string): string => {
    // Remove any numbers and extra spaces
    const nameOnly = street.replace(/\d+/g, '').replace(/^\s+|\s+$/g, '').replace(/\s+/g, ' ')

    // Remove any leading commas, periods, or other punctuation
    const cleanName = nameOnly.replace(/^[,.\s]+/, '')

    return cleanName
  }

  // Helper function to extract a detailed area name from an address (neighborhood-city format)
  const extractAreaName = (address: string): string => {
    if (!address) return ''

    // Split the address by commas
    const parts = address.split(',').map(part => part.trim())

    // Find parts without numbers (likely neighborhood, area, city names)
    const cleanParts = parts.filter(part => part && !/\d/.test(part))

    if (cleanParts.length >= 2) {
      // If we have at least two clean parts (like neighborhood and city)
      // Format as "Neighborhood-City"
      return `${cleanParts[0]}-${cleanParts[1]}`
    } else if (cleanParts.length === 1) {
      // If we only have one clean part
      return cleanParts[0]
    }

    // If all parts contain numbers, return the first part
    return parts[0] || ''
  }

  const handleLocationGranted = useCallback(async (location: Location.LocationObject) => {
    try {
      console.log('Location permission granted, processing location...')

      // Get address from coordinates
      const [address] = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      })

      if (address) {
        // Generate a friendly location name
        const locationName = generateFriendlyLocationName(address)

        // Format the full address in a readable way
        const formattedAddress = [
          address.name,
          address.street,
          address.district,
          address.city,
          address.region,
          address.postalCode,
          address.country
        ]
          .filter(Boolean)
          .join(', ')

        // Format the area in neighborhood-city format
        let areaName = ''
        if (address.district && address.city) {
          areaName = `${address.district}-${address.city}`
        } else if (address.subregion && address.city) {
          areaName = `${address.subregion}-${address.city}`
        } else if (address.name && address.city && !containsNumbers(address.name)) {
          areaName = `${address.name}-${address.city}`
        } else {
          areaName = address.city || address.region || ''
        }

        // Create a location object
        const newLocation = {
          id: `current_${Date.now()}`,
          label: locationName || 'Current Location',
          address: formattedAddress,
          area: areaName,
          coordinates: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          isPrimary: true
        }

        // Add location to context
        await addLocation(newLocation)

        console.log('Location added successfully:', {
          label: newLocation.label,
          address: newLocation.address
        })

        // Save the location permission status to prevent showing the modal again
        await Storage.setItem(STORAGE_KEYS.LOCATION_PERMISSION, 'granted')
      }
    } catch (error) {
      console.error('Error handling location permission:', error)
    }
    // Note: We don't need to set modal visibility here as it's now handled in the modal component
  }, [addLocation])

  // Handle location permission denied
  const handleLocationDenied = useCallback(() => {
    console.log('Location permission denied, navigating to location selection')
    // Note: We don't need to set modal visibility here as it's now handled in the modal component

    // Save the location permission status to prevent showing the modal again
    Storage.setItem(STORAGE_KEYS.LOCATION_PERMISSION, 'denied')

    // Navigate to location selection screen
    navigation.navigate('LocationSelection')
  }, [navigation])

  // Function to animate search placeholder text
  const animateSearchPlaceholder = () => {
    // Fade out current text
    Animated.timing(searchTextOpacity, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      // Change text and fade back in
      setSearchPlaceholderIndex((prevIndex) => (prevIndex + 1) % searchKeywords.length)
      Animated.timing(searchTextOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start()
    })
  }

  // Handle search submission
  const handleSearchSubmit = () => {
    try {
      // Ensure we're passing a string as the search query
      if (searchText.trim()) {
        // Navigate with a delay to allow keyboard to dismiss
        setTimeout(() => {
          navigation.navigate("ServiceBrowsing", { searchQuery: searchText.trim() })
        }, 100)
      } else {
        // If empty, use the current placeholder as search query
        const placeholderQuery = searchKeywords[searchPlaceholderIndex] || ''
        setTimeout(() => {
          navigation.navigate("ServiceBrowsing", { searchQuery: placeholderQuery })
        }, 100)
      }
      // Clear focus and reset search text
      setIsSearchFocused(false)
      setSearchText('')
    } catch (error) {
      console.error('Navigation error:', error)
      // Fallback navigation with empty params
      setTimeout(() => {
        navigation.navigate("ServiceBrowsing", {})
      }, 100)
    }
  }

  // Set up search placeholder animation interval
  useEffect(() => {
    // Only animate when not focused and no text entered
    if (!isSearchFocused && !searchText) {
      const searchAnimationTimer = setInterval(() => {
        animateSearchPlaceholder()
      }, 4000) // Change text every 4 seconds

      return () => clearInterval(searchAnimationTimer)
    }
  }, [isSearchFocused, searchText])

  // Auto-slide interval in milliseconds
  const AUTO_SLIDE_INTERVAL = 4000 // 4 seconds

  // Function to handle auto-sliding with smooth looping
  const goToNextSlide = () => {
    if (promotionRef.current && promotionBanners.length > 0) {
      const nextIndex = (activePromotionIndex + 1) % promotionBanners.length

      // If transitioning from last to first slide, create an illusion of continuity
      if (activePromotionIndex === promotionBanners.length - 1 && nextIndex === 0) {
        const slideWidth = width - theme.spacing.md * 2

        // Scroll to the cloned first slide (which is after the last slide)
        promotionRef.current.scrollToOffset({
          offset: slideWidth * promotionBanners.length,
          animated: true,
        })

        // After animation completes, silently reset to the actual first slide
        setTimeout(() => {
          promotionRef.current?.scrollToOffset({
            offset: 0,
            animated: false,
          })
          setActivePromotionIndex(0)
        }, 300) // Match this with your transition duration
      } else {
        // Normal slide transition
        promotionRef.current.scrollToIndex({
          index: nextIndex,
          animated: true,
        })
        setActivePromotionIndex(nextIndex)
      }
    }
  }

  // Set up auto-sliding with useEffect
  useEffect(() => {
    const timer = setInterval(() => {
      goToNextSlide()
    }, AUTO_SLIDE_INTERVAL)

    // Clean up the timer when component unmounts
    return () => clearInterval(timer)
  }, [activePromotionIndex])

  // Handle manual scroll end
  const handleScrollEnd = (event: { nativeEvent: { contentOffset: { x: number } } }) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x
    const slideWidth = width - theme.spacing.md * 2
    const index = Math.round(contentOffsetX / slideWidth)

    // If we scrolled past the end
    if (index >= promotionBanners.length) {
      // Silently snap to the first slide
      promotionRef.current?.scrollToOffset({
        offset: 0,
        animated: false,
      })
      setActivePromotionIndex(0)
    }
    // If we scrolled before the beginning
    else if (index < 0) {
      // Silently snap to the last slide
      promotionRef.current?.scrollToOffset({
        offset: slideWidth * (promotionBanners.length - 1),
        animated: false,
      })
      setActivePromotionIndex(promotionBanners.length - 1)
    }
    // Regular position
    else if (index !== activePromotionIndex) {
      setActivePromotionIndex(index)
    }
  }

  // Promotion banners data
  const promotionBanners = [
    {
      id: '1',
      image: 'https://picsum.photos/id/42/800/400',
      title: 'Summer Cleaning Special',
      description: '20% off on all cleaning services this summer!',
    },
    {
      id: '2',
      image: 'https://picsum.photos/id/26/800/400',
      title: 'New Customer Discount',
      description: 'First-time customers get 15% off!',
    },
    {
      id: '3',
      image: 'https://picsum.photos/id/24/800/400',
      title: 'Premium Deep Cleaning',
      description: 'Book our premium deep cleaning service today!',
    },
  ]

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      flex: 1,
    },
    statusBarSpace: {
      height: insets.top,
      backgroundColor: theme.colors.primary,
    },
    header: {
      backgroundColor: theme.colors.primary,
      padding: theme.spacing.md,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    userInfo: {
      flexDirection: "row",
      alignItems: "center",
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: theme.spacing.sm,
    },
    userTextContainer: {
      justifyContent: "center",
    },
    appTitle: {
      fontSize: 18,
      fontWeight: "700",
      color: "white",
      marginBottom:2,
    },
    locationSelector: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 2,
      maxWidth: width * 0.95, // Increased width to show location name horizontally
    },
    locationLoading: {
      flexDirection: "row",
      alignItems: "center",
    },
    locationText: {
      fontSize: 12,
      color: "rgba(255, 255, 255, 0.9)",
      fontWeight: "500",
    },
    locationIcon: {
      marginLeft: 8,
    },
    notificationButton: {
      position: "relative",
      padding: theme.spacing.xs,
    },
    notificationBadge: {
      position: "absolute",
      top: 0,
      right: 0,
      backgroundColor: theme.colors.warning,
      width: 16,
      height: 16,
      borderRadius: 8,
      justifyContent: "center",
      alignItems: "center",
    },
    notificationCount: {
      color: "black",
      fontSize: 10,
      fontWeight: "700",
    },
    content: {
      padding: theme.spacing.md,
      paddingTop: Math.round(height * 0.02), // Additional 2% top padding for content
    },

    searchContainer: {
      alignItems: "center",
      backgroundColor: theme.colors.card,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1.5,
      flexDirection: "row",
      marginBottom: theme.spacing.md,
      paddingHorizontal: theme.spacing.md,
      height: 50, // Fixed height for consistency
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    searchIcon: {
      color: theme.colors.textLight,
      marginRight: theme.spacing.sm,
    },
    searchInputPrefix: {
      color: theme.colors.text,
      fontSize: theme.fontSizes.sm,
      fontWeight: "400",
    },
    searchInputKeyword: {
      color: theme.colors.text,
      fontSize: theme.fontSizes.sm,
      fontWeight: "600",
    },
    searchInput: {
      flex: 1,
      fontSize: theme.fontSizes.sm,
      color: theme.colors.text,
      height: 40, // Fixed height for input
      padding: 0, // Remove default padding
    },
    searchTextContainer: {
      flexDirection: "row",
      flex: 1,
    },

    promotionCarouselContainer: {
      marginBottom: theme.spacing.xs,
    },
    promotionSlide: {
      width: width - theme.spacing.md * 2,
      height: Math.round(height * 0.22),
      borderRadius: theme.borderRadius.md,
      overflow: 'hidden',
    },
    promotionImage: {
      width: '100%',
      height: '100%',
    },
    promotionContent: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      padding: theme.spacing.md,
      backgroundColor: 'rgba(0,0,0,0.4)',
    },
    promotionTitle: {
      color: 'white',
      fontSize: theme.fontSizes.lg,
      fontWeight: '700',
      marginBottom: 4,
    },
    promotionDescription: {
      color: 'rgba(255,255,255,0.9)',
      fontSize: theme.fontSizes.sm,
    },
    paginationContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: theme.spacing.sm,
      marginBottom: theme.spacing.md,
    },
    paginationDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
      backgroundColor: theme.colors.border,
    },
    paginationDotActive: {
      backgroundColor: theme.colors.primary,
      width: 12,
      height: 12,
      borderRadius: 6,
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      fontSize: theme.fontSizes.lg,
      fontWeight: "600",
      color: theme.colors.text,
    },
    viewAllButton: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.primary,
    },
    quickBookingContainer: {
      marginBottom: theme.spacing.lg,
    },
    quickBookingList: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    quickBookingItem: {
      alignItems: "center",
      width: "22%",
    },
    quickBookingItemActive: {
      opacity: 0.8,
      transform: [{ scale: 0.95 }],
    },
    quickBookingIconContainer: {
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: `${theme.colors.accent}30`,
      justifyContent: "center",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    quickBookingText: {
      fontSize: theme.fontSizes.xs,
      textAlign: "center",
    },
    // Removed unused cleaner styles
    // Removed unused styles


    // Hero styles
    heroesContainer: {
      marginBottom: theme.spacing.md,
    },
    heroCard: {
      marginBottom: theme.spacing.md,
      padding: 0,
      overflow: "hidden",
      borderRadius: 12,
    },
    heroCardTouchable: {
      flexDirection: "row",
      alignItems: "center",
    },
    heroImageContainer: {
      position: "relative",
      width: 70,
      height: 70,
      borderRadius: 35,
      overflow: "hidden",
      margin: 12,
    },
    heroAvatar: {
      width: "100%",
      height: "100%",
    },
    heroBadge: {
      position: "absolute",
      top: 0,
      right: 0,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 8,
    },
    heroBadgeText: {
      fontSize: 8,
      fontWeight: "700",
      color: "#000",
    },
    heroContent: {
      flex: 1,
      padding: 12,
    },
    heroNameRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    heroName: {
      fontSize: 16,
      fontWeight: "600",
      color: theme.colors.text,
      marginRight: 8,
    },
    verifiedBadge: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 10,
    },
    verifiedText: {
      fontSize: 10,
      fontWeight: "600",
    },
    heroDetailsRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    heroRatingContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    heroStar: {
      color: "#FFD700",
      marginRight: 4,
    },
    heroRatingText: {
      fontSize: 14,
      fontWeight: "500",
    },
    heroReviewsText: {
      fontSize: 12,
      color: theme.colors.textLight,
      marginLeft: 4,
    },
    heroPrice: {
      fontSize: 12,
      fontWeight: "500",
      color: theme.colors.text,
    },
    heroChevron: {
      marginRight: 12,
    },
  })

  const handleQuickBookingPress = async (serviceId: string) => {
    // Set loading state for this specific service
    setLoadingServiceId(serviceId)

    try {
      // Simulate a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 300))

      // Navigate to service detail screen
      navigation.navigate("ServiceDetail", {
        serviceId,
        transition: "slide"
      })
    } finally {
      // Clear loading state
      setLoadingServiceId(null)
    }
  }

  // Handle hero press - navigate to hero detail screen
  const handleHeroPress = (heroId: string) => {
    navigation.navigate("HeroDetail", {
      heroId: heroId,
    })
  }

  // We've removed the handleBookHero function since we're now navigating to the hero detail page instead



  // Mobile layout


  return (
    <View style={styles.container}>
      {/* Location Permission Modal */}
      <LocationPermissionModal
        visible={showLocationPermissionModal}
        onClose={() => setShowLocationPermissionModal(false)}
        onLocationGranted={handleLocationGranted}
        onLocationDenied={handleLocationDenied}
      />

      {/* Status Bar Space */}
      <View style={styles.statusBarSpace} />

      {/* App Header */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Image source={{ uri: "https://randomuser.me/api/portraits/women/67.jpg" }} style={styles.avatar} />
          <View style={styles.userTextContainer}>
            <Text style={styles.appTitle}>Hi, Fatou</Text>
            <TouchableOpacity
              style={styles.locationSelector}
              onPress={() => navigation.navigate("LocationSelection")}
              activeOpacity={0.7}
              disabled={isLocationLoading}
            >
              {isLocationLoading ? (
                <View style={styles.locationLoading}>
                  <ActivityIndicator size="small" color="rgba(255, 255, 255, 0.8)" />
                  <Text style={styles.locationText}>Loading location...</Text>
                </View>
              ) : (
                <>
                  <Text style={styles.locationText}>
                    {currentLocation
                      ? (currentLocation.area && currentLocation.area.includes('-')
                          ? currentLocation.area
                          : extractAreaName(currentLocation.address) || currentLocation.label)
                      : 'Select location'}
                  </Text>
                  <FontAwesome5 name="chevron-down" size={12} color="rgba(255, 255, 255, 1)" solid style={[styles.locationIcon, { marginLeft: 4 }]} />
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
        <TouchableOpacity
          style={styles.notificationButton}
          onPress={() => navigation.navigate("Notifications")}
        >
          <Feather name="bell" size={24} color="white" />
          <View style={styles.notificationBadge}>
            <Text style={styles.notificationCount}>3</Text>
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Search Bar with Animated Placeholder */}
        <View style={styles.searchContainer}>
          <Feather name="search" size={20} style={styles.searchIcon} />
          {isSearchFocused || searchText ? (
            <TextInput
              style={styles.searchInput}
              placeholder={`Search for ${searchKeywords[searchPlaceholderIndex]}...`}
              placeholderTextColor={theme.colors.textLight}
              value={searchText}
              onChangeText={setSearchText}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              onSubmitEditing={handleSearchSubmit}
              returnKeyType="search"
              autoCapitalize="none"
              clearButtonMode="while-editing"
              enablesReturnKeyAutomatically={true}
            />
          ) : (
            <TouchableOpacity
              style={styles.searchTextContainer}
              onPress={() => setIsSearchFocused(true)}
              activeOpacity={0.7}
              accessible={true}
              accessibilityLabel="Search for services"
              accessibilityHint="Tap to search for cleaning services"
            >
              <Text style={[styles.searchInputPrefix, { color: theme.colors.textLight }]}>
                Search for{' '}
              </Text>
              <Animated.Text
                style={[
                  styles.searchInputKeyword,
                  { color: theme.colors.textLight, opacity: searchTextOpacity }
                ]}
              >
                {searchKeywords[searchPlaceholderIndex]}
              </Animated.Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Promotion Carousel */}
        <View style={styles.promotionCarouselContainer}>
          <Animated.View style={{
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }}>
            <FlatList
              ref={promotionRef}
              data={promotionBanners}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              pagingEnabled
              onMomentumScrollEnd={handleScrollEnd}
              renderItem={({ item }) => (
                <View style={styles.promotionSlide}>
                  <Image
                    source={{ uri: item.image }}
                    style={styles.promotionImage}
                    resizeMode="cover"
                  />
                  <View style={styles.promotionContent}>
                    <Text style={styles.promotionTitle}>{item.title}</Text>
                    <Text style={styles.promotionDescription}>{item.description}</Text>
                  </View>
                </View>
              )}
            />
          </Animated.View>

          {/* Pagination Dots */}
          <View style={styles.paginationContainer}>
            {promotionBanners.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.paginationDot,
                  index === activePromotionIndex && styles.paginationDotActive,
                ]}
              />
            ))}
          </View>
        </View>

        {/* Quick Booking */}
        <View style={styles.quickBookingContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Quick Booking</Text>
          </View>
          {isServicesLoading ? (
            <View style={{ alignItems: 'center', padding: theme.spacing.md }}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={{ marginTop: theme.spacing.sm, color: theme.colors.textLight }}>Loading services...</Text>
            </View>
          ) : servicesError ? (
            <View style={{ alignItems: 'center', padding: theme.spacing.md }}>
              <Feather name="alert-circle" size={24} color={theme.colors.notification} />
              <Text style={{ marginTop: theme.spacing.sm, color: theme.colors.textLight }}>{servicesError}</Text>
              <TouchableOpacity
                style={{ marginTop: theme.spacing.sm }}
                onPress={() => fetchServices(true)}
              >
                <Text style={{ color: theme.colors.primary }}>Try Again</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.quickBookingList}>
              {services.slice(0, 4).map((service) => (
                <TouchableOpacity
                  key={service.id}
                  style={[styles.quickBookingItem, loadingServiceId === service.id && styles.quickBookingItemActive]}
                  onPress={() => handleQuickBookingPress(service.id)}
                  disabled={loadingServiceId === service.id}
                >
                  <View style={styles.quickBookingIconContainer}>
                    {loadingServiceId === service.id ? (
                      <ActivityIndicator size="small" color={theme.colors.primary} />
                    ) : (
                      <Feather name={(service.icon as any) || "home"} size={28} color={theme.colors.accent} />
                    )}
                  </View>
                  <Text style={styles.quickBookingText}>{service.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* CleanConnect Heroes */}
        <View>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Our CleanConnect Heroes</Text>
            <TouchableOpacity onPress={() => navigation.navigate("AllHeroes")}>
              <Text style={styles.viewAllButton}>View all</Text>
            </TouchableOpacity>
          </View>
          {isProvidersLoading ? (
            <View style={{ alignItems: 'center', padding: theme.spacing.md }}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={{ marginTop: theme.spacing.sm, color: theme.colors.textLight }}>Loading providers...</Text>
            </View>
          ) : providersError ? (
            <View style={{ alignItems: 'center', padding: theme.spacing.md }}>
              <Feather name="alert-circle" size={24} color={theme.colors.notification} />
              <Text style={{ marginTop: theme.spacing.sm, color: theme.colors.textLight }}>{providersError}</Text>
              <TouchableOpacity
                style={{ marginTop: theme.spacing.sm }}
                onPress={() => fetchProviders(true)}
              >
                <Text style={{ color: theme.colors.primary }}>Try Again</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.heroesContainer}>
              {providers.map((hero) => (
                <Card key={hero.id} style={styles.heroCard}>
                  <TouchableOpacity
                    onPress={() => handleHeroPress(hero.id)}
                    style={styles.heroCardTouchable}
                  >
                    <View style={styles.heroImageContainer}>
                      <Image source={{ uri: hero.image }} style={styles.heroAvatar} />
                      {hero.rating >= 4.8 && (
                        <View style={[styles.heroBadge, { backgroundColor: theme.colors.warning }]}>
                          <Text style={styles.heroBadgeText}>Top Hero</Text>
                        </View>
                      )}
                    </View>
                    <View style={styles.heroContent}>
                      <View style={styles.heroNameRow}>
                        <Text style={styles.heroName}>{hero.name}</Text>
                        {hero.verified && (
                          <View style={[styles.verifiedBadge, { backgroundColor: `${theme.colors.primary}15` }]}>
                            <Feather name="check-circle" size={12} color={theme.colors.primary} style={{ marginRight: 2 }} />
                            <Text style={[styles.verifiedText, { color: theme.colors.primary }]}>Verified</Text>
                          </View>
                        )}
                      </View>
                      <View style={styles.heroDetailsRow}>
                        <View style={styles.heroRatingContainer}>
                          <Feather name="star" size={14} style={styles.heroStar} />
                          <Text style={styles.heroRatingText}>{hero.rating}</Text>
                          <Text style={styles.heroReviewsText}>({hero.jobs} jobs)</Text>
                        </View>
                      </View>
                    </View>
                    <Feather name="chevron-right" size={18} color={theme.colors.textLight} style={styles.heroChevron} />
                  </TouchableOpacity>
                </Card>
              ))}
            </View>
          )}
        </View>

      </ScrollView>

      {/* Location selection is now handled by the LocationSelectionScreen */}

      {/* Location Permission Modal */}
      <LocationPermissionModal
        visible={showLocationPermissionModal}
        onClose={() => setShowLocationPermissionModal(false)}
        onLocationGranted={handleLocationGranted}
        onLocationDenied={handleLocationDenied}
      />
    </View>
  )
}

export default HomeScreen
