import { bookingService } from '../api/services';
import { Booking } from '../types/booking';
import { BookingStatus } from '../types/booking';
import { format } from 'date-fns';

// Interface for the UI booking item
export interface BookingHistoryItem {
  id: string;
  service: string;
  date: string;
  time: string;
  address: string;
  status: string;
  price: number;
  cleaner: {
    name: string;
    image: string;
  };
}

// Convert booking data to UI booking items
export const convertBookingsToHistoryItems = (
  bookings: Booking[]
): { upcoming: BookingHistoryItem[]; past: BookingHistoryItem[] } => {
  const upcoming: BookingHistoryItem[] = [];
  const past: BookingHistoryItem[] = [];

  bookings.forEach(booking => {
    // Create a booking history item
    const historyItem: BookingHistoryItem = {
      id: booking.id,
      service: booking.service?.title || `Cleaning Service (${booking.serviceId})`,
      date: booking.date ? new Date(booking.date).toLocaleDateString() : '',
      time: booking.startTime ?
        `${new Date(booking.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - ${new Date(booking.endTime || booking.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}` :
        '',
      address: booking.address?.address || booking.addressId || '',
      status: getStatusText(booking.status),
      price: booking.price || 0,
      cleaner: {
        name: booking.provider?.user?.firstName ?
          `${booking.provider.user.firstName} ${booking.provider.user.lastName || ''}` :
          'Auto-assigned',
        image: booking.provider?.user?.profileImage || 'https://randomuser.me/api/portraits/men/32.jpg'
      }
    };

    // Add to the appropriate list
    if (
      booking.status === BookingStatus.PENDING ||
      booking.status === BookingStatus.CONFIRMED ||
      booking.status === BookingStatus.IN_PROGRESS
    ) {
      upcoming.push(historyItem);
    } else {
      past.push(historyItem);
    }
  });

  return { upcoming, past };
};

// Get status text from booking status
const getStatusText = (status: BookingStatus): string => {
  switch (status) {
    case BookingStatus.PENDING:
      return 'Pending';
    case BookingStatus.CONFIRMED:
      return 'Confirmed';
    case BookingStatus.IN_PROGRESS:
      return 'In Progress';
    case BookingStatus.COMPLETED:
      return 'Completed';
    case BookingStatus.CANCELLED:
      return 'Cancelled';
    case BookingStatus.REJECTED:
      return 'Rejected';
    default:
      return 'Unknown';
  }
};

// Get booking history from real API
export const getBookingHistory = async (): Promise<{ upcoming: BookingHistoryItem[]; past: BookingHistoryItem[] }> => {
  try {
    // Get real bookings from API
    const bookings = await bookingService.getBookings();

    // Convert to history items
    return convertBookingsToHistoryItems(bookings);
  } catch (error) {
    console.error('Error getting booking history:', error);
    return { upcoming: [], past: [] };
  }
};
